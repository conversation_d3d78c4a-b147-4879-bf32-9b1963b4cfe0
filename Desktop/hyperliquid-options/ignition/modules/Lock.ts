import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";
import { parseEther } from "viem";

// const JAN_1ST_2030 = 1893456000;
// const ONE_GWEI: bigint = parseEther("0.001");

const DeploymentModule = buildModule("LockModule", (m) => {
  // const unlockTime = m.getParameter("unlockTime", JAN_1ST_2030);
  // const lockedAmount = m.getParameter("lockedAmount", ONE_GWEI);
  // const lock = m.contract("HelloWorld",["Why are u geh?"]);
  const lock = m.contract("ReadPrices");

  return { lock };
});

export default DeploymentModule;
