{"id": "b079b62b386b54dad0a25dc0ee3fbaa9", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.28", "solcLongVersion": "0.8.28+commit.7893614a", "input": {"language": "Solidity", "sources": {"contracts/Please.sol": {"content": "// SPDX-License-Identifier: UNLICENSED\npragma solidity ^0.8.28;\n\ncontract Please {\n    \n    // Find which asset indices work on testnet\n    function findWorkingAssets() external view returns (uint32[] memory workingIndices, uint[] memory rawPrices) {\n        uint32[] memory tempIndices = new uint32[](20);\n        uint[] memory tempPrices = new uint[](20);\n        uint count = 0;\n        \n        // Test indices 0-19\n        for (uint32 i = 0; i < 20; i++) {\n            (bool success, bytes memory result) = address(******************************************).staticcall(abi.encode(i));\n            \n            if (success) {\n                uint64 price = abi.decode(result, (uint64));\n                tempIndices[count] = i;\n                tempPrices[count] = uint(price);\n                count++;\n            }\n        }\n        \n        // Return only working assets\n        workingIndices = new uint32[](count);\n        rawPrices = new uint[](count);\n        for (uint i = 0; i < count; i++) {\n            workingIndices[i] = tempIndices[i];\n            rawPrices[i] = tempPrices[i];\n        }\n    }\n    \n    // Your simple working function\n    function getPrice(uint32 assetIndex) external view returns (uint) {\n        (bool success, bytes memory result) = address(******************************************).staticcall(abi.encode(assetIndex));\n        require(success, \"Asset doesn't exist\");\n        \n        uint64 rawPrice = abi.decode(result, (uint64));\n        return uint(rawPrice) / 10; // Your working conversion\n    }\n}"}}, "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"contracts/Please.sol": {"ast": {"absolutePath": "contracts/Please.sol", "exportedSymbols": {"Please": [185]}, "id": 186, "license": "UNLICENSED", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".28"], "nodeType": "PragmaDirective", "src": "39:24:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "Please", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 185, "linearizedBaseContracts": [185], "name": "Please", "nameLocation": "74:6:0", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 138, "nodeType": "Block", "src": "249:861:0", "statements": [{"assignments": [14], "declarations": [{"constant": false, "id": 14, "mutability": "mutable", "name": "tempIndices", "nameLocation": "275:11:0", "nodeType": "VariableDeclaration", "scope": 138, "src": "259:27:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[]"}, "typeName": {"baseType": {"id": 12, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "259:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 13, "nodeType": "ArrayTypeName", "src": "259:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_storage_ptr", "typeString": "uint32[]"}}, "visibility": "internal"}], "id": 20, "initialValue": {"arguments": [{"hexValue": "3230", "id": 18, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "302:2:0", "typeDescriptions": {"typeIdentifier": "t_rational_20_by_1", "typeString": "int_const 20"}, "value": "20"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_20_by_1", "typeString": "int_const 20"}], "id": 17, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "289:12:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint32_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint32[] memory)"}, "typeName": {"baseType": {"id": 15, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "293:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 16, "nodeType": "ArrayTypeName", "src": "293:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_storage_ptr", "typeString": "uint32[]"}}}, "id": 19, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "289:16:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "259:46:0"}, {"assignments": [25], "declarations": [{"constant": false, "id": 25, "mutability": "mutable", "name": "tempPrices", "nameLocation": "329:10:0", "nodeType": "VariableDeclaration", "scope": 138, "src": "315:24:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 23, "name": "uint", "nodeType": "ElementaryTypeName", "src": "315:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 24, "nodeType": "ArrayTypeName", "src": "315:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "id": 31, "initialValue": {"arguments": [{"hexValue": "3230", "id": 29, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "353:2:0", "typeDescriptions": {"typeIdentifier": "t_rational_20_by_1", "typeString": "int_const 20"}, "value": "20"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_20_by_1", "typeString": "int_const 20"}], "id": 28, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "342:10:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint256[] memory)"}, "typeName": {"baseType": {"id": 26, "name": "uint", "nodeType": "ElementaryTypeName", "src": "346:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 27, "nodeType": "ArrayTypeName", "src": "346:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "id": 30, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "342:14:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "315:41:0"}, {"assignments": [33], "declarations": [{"constant": false, "id": 33, "mutability": "mutable", "name": "count", "nameLocation": "371:5:0", "nodeType": "VariableDeclaration", "scope": 138, "src": "366:10:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 32, "name": "uint", "nodeType": "ElementaryTypeName", "src": "366:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 35, "initialValue": {"hexValue": "30", "id": 34, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "379:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "366:14:0"}, {"body": {"id": 92, "nodeType": "Block", "src": "460:369:0", "statements": [{"assignments": [47, 49], "declarations": [{"constant": false, "id": 47, "mutability": "mutable", "name": "success", "nameLocation": "480:7:0", "nodeType": "VariableDeclaration", "scope": 92, "src": "475:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 46, "name": "bool", "nodeType": "ElementaryTypeName", "src": "475:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 49, "mutability": "mutable", "name": "result", "nameLocation": "502:6:0", "nodeType": "VariableDeclaration", "scope": 92, "src": "489:19:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 48, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "489:5:0", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 60, "initialValue": {"arguments": [{"arguments": [{"id": 57, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 37, "src": "586:1:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "expression": {"id": 55, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "575:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 56, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "579:6:0", "memberName": "encode", "nodeType": "MemberAccess", "src": "575:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "575:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"arguments": [{"hexValue": "307830303030303030303030303030303030303030303030303030303030303030303030303030383037", "id": 52, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "520:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "******************************************"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 51, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "512:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 50, "name": "address", "nodeType": "ElementaryTypeName", "src": "512:7:0", "typeDescriptions": {}}}, "id": 53, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "512:51:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "564:10:0", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "512:62:0", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 59, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "512:77:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "474:115:0"}, {"condition": {"id": 61, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 47, "src": "620:7:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 91, "nodeType": "IfStatement", "src": "616:203:0", "trueBody": {"id": 90, "nodeType": "Block", "src": "629:190:0", "statements": [{"assignments": [63], "declarations": [{"constant": false, "id": 63, "mutability": "mutable", "name": "price", "nameLocation": "654:5:0", "nodeType": "VariableDeclaration", "scope": 90, "src": "647:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 62, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "647:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 71, "initialValue": {"arguments": [{"id": 66, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 49, "src": "673:6:0", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 68, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "682:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 67, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "682:6:0", "typeDescriptions": {}}}], "id": 69, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "681:8:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}], "expression": {"id": 64, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "662:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 65, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "666:6:0", "memberName": "decode", "nodeType": "MemberAccess", "src": "662:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 70, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "662:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "647:43:0"}, {"expression": {"id": 76, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 72, "name": "tempIndices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "708:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "id": 74, "indexExpression": {"id": 73, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "720:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "708:18:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 75, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 37, "src": "729:1:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "src": "708:22:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 77, "nodeType": "ExpressionStatement", "src": "708:22:0"}, {"expression": {"id": 85, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 78, "name": "tempPrices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "748:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 80, "indexExpression": {"id": 79, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "759:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "748:17:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 83, "name": "price", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 63, "src": "773:5:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 82, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "768:4:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 81, "name": "uint", "nodeType": "ElementaryTypeName", "src": "768:4:0", "typeDescriptions": {}}}, "id": 84, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "768:11:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "748:31:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 86, "nodeType": "ExpressionStatement", "src": "748:31:0"}, {"expression": {"id": 88, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "797:7:0", "subExpression": {"id": 87, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "797:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 89, "nodeType": "ExpressionStatement", "src": "797:7:0"}]}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 42, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 40, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 37, "src": "447:1:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "3230", "id": 41, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "451:2:0", "typeDescriptions": {"typeIdentifier": "t_rational_20_by_1", "typeString": "int_const 20"}, "value": "20"}, "src": "447:6:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 93, "initializationExpression": {"assignments": [37], "declarations": [{"constant": false, "id": 37, "mutability": "mutable", "name": "i", "nameLocation": "440:1:0", "nodeType": "VariableDeclaration", "scope": 93, "src": "433:8:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 36, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "433:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "id": 39, "initialValue": {"hexValue": "30", "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "444:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "433:12:0"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 44, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "455:3:0", "subExpression": {"id": 43, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 37, "src": "455:1:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 45, "nodeType": "ExpressionStatement", "src": "455:3:0"}, "nodeType": "ForStatement", "src": "428:401:0"}, {"expression": {"id": 100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 94, "name": "workingIndices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 5, "src": "885:14:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 98, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "915:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 97, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "902:12:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint32_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint32[] memory)"}, "typeName": {"baseType": {"id": 95, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "906:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 96, "nodeType": "ArrayTypeName", "src": "906:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_storage_ptr", "typeString": "uint32[]"}}}, "id": 99, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "902:19:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "src": "885:36:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "id": 101, "nodeType": "ExpressionStatement", "src": "885:36:0"}, {"expression": {"id": 108, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 102, "name": "rawPrices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "931:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 106, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "954:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 105, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "943:10:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_uint256_$dyn_memory_ptr_$", "typeString": "function (uint256) pure returns (uint256[] memory)"}, "typeName": {"baseType": {"id": 103, "name": "uint", "nodeType": "ElementaryTypeName", "src": "947:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 104, "nodeType": "ArrayTypeName", "src": "947:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}}, "id": 107, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "943:17:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "src": "931:29:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 109, "nodeType": "ExpressionStatement", "src": "931:29:0"}, {"body": {"id": 136, "nodeType": "Block", "src": "1003:101:0", "statements": [{"expression": {"id": 126, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 120, "name": "workingIndices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 5, "src": "1017:14:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "id": 122, "indexExpression": {"id": 121, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "1032:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1017:17:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 123, "name": "tempIndices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "1037:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[] memory"}}, "id": 125, "indexExpression": {"id": 124, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "1049:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1037:14:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "src": "1017:34:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 127, "nodeType": "ExpressionStatement", "src": "1017:34:0"}, {"expression": {"id": 134, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 128, "name": "rawPrices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "1065:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 130, "indexExpression": {"id": 129, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "1075:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1065:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"baseExpression": {"id": 131, "name": "tempPrices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "1080:10:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[] memory"}}, "id": 133, "indexExpression": {"id": 132, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "1091:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1080:13:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1065:28:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 135, "nodeType": "ExpressionStatement", "src": "1065:28:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 116, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 114, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "987:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 115, "name": "count", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "991:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "987:9:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 137, "initializationExpression": {"assignments": [111], "declarations": [{"constant": false, "id": 111, "mutability": "mutable", "name": "i", "nameLocation": "980:1:0", "nodeType": "VariableDeclaration", "scope": 137, "src": "975:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 110, "name": "uint", "nodeType": "ElementaryTypeName", "src": "975:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 113, "initialValue": {"hexValue": "30", "id": 112, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "984:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "975:10:0"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 118, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "998:3:0", "subExpression": {"id": 117, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 111, "src": "998:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 119, "nodeType": "ExpressionStatement", "src": "998:3:0"}, "nodeType": "ForStatement", "src": "970:134:0"}]}, "functionSelector": "30320dd4", "id": 139, "implemented": true, "kind": "function", "modifiers": [], "name": "findWorkingAssets", "nameLocation": "149:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 2, "nodeType": "ParameterList", "parameters": [], "src": "166:2:0"}, "returnParameters": {"id": 9, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 5, "mutability": "mutable", "name": "workingIndices", "nameLocation": "208:14:0", "nodeType": "VariableDeclaration", "scope": 139, "src": "192:30:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_memory_ptr", "typeString": "uint32[]"}, "typeName": {"baseType": {"id": 3, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "192:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 4, "nodeType": "ArrayTypeName", "src": "192:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$dyn_storage_ptr", "typeString": "uint32[]"}}, "visibility": "internal"}, {"constant": false, "id": 8, "mutability": "mutable", "name": "rawPrices", "nameLocation": "238:9:0", "nodeType": "VariableDeclaration", "scope": 139, "src": "224:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_memory_ptr", "typeString": "uint256[]"}, "typeName": {"baseType": {"id": 6, "name": "uint", "nodeType": "ElementaryTypeName", "src": "224:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 7, "nodeType": "ArrayTypeName", "src": "224:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$dyn_storage_ptr", "typeString": "uint256[]"}}, "visibility": "internal"}], "src": "191:57:0"}, "scope": 185, "src": "140:970:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 183, "nodeType": "Block", "src": "1222:318:0", "statements": [{"assignments": [147, 149], "declarations": [{"constant": false, "id": 147, "mutability": "mutable", "name": "success", "nameLocation": "1238:7:0", "nodeType": "VariableDeclaration", "scope": 183, "src": "1233:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 146, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1233:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 149, "mutability": "mutable", "name": "result", "nameLocation": "1260:6:0", "nodeType": "VariableDeclaration", "scope": 183, "src": "1247:19:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 148, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "1247:5:0", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 160, "initialValue": {"arguments": [{"arguments": [{"id": 157, "name": "assetIndex", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 141, "src": "1344:10:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "expression": {"id": 155, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1333:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 156, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1337:6:0", "memberName": "encode", "nodeType": "MemberAccess", "src": "1333:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abiencode_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 158, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1333:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"arguments": [{"hexValue": "307830303030303030303030303030303030303030303030303030303030303030303030303030383037", "id": 152, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1278:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "******************************************"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 151, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1270:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 150, "name": "address", "nodeType": "ElementaryTypeName", "src": "1270:7:0", "typeDescriptions": {}}}, "id": 153, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1270:51:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 154, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1322:10:0", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "1270:62:0", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 159, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1270:86:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "1232:124:0"}, {"expression": {"arguments": [{"id": 162, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 147, "src": "1374:7:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "417373657420646f65736e2774206578697374", "id": 163, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1383:21:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc", "typeString": "literal_string \"Asset doesn't exist\""}, "value": "Asset doesn't exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc", "typeString": "literal_string \"Asset doesn't exist\""}], "id": 161, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18, -18], "referencedDeclaration": -18, "src": "1366:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 164, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1366:39:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 165, "nodeType": "ExpressionStatement", "src": "1366:39:0"}, {"assignments": [167], "declarations": [{"constant": false, "id": 167, "mutability": "mutable", "name": "rawPrice", "nameLocation": "1431:8:0", "nodeType": "VariableDeclaration", "scope": 183, "src": "1424:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 166, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1424:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 175, "initialValue": {"arguments": [{"id": 170, "name": "result", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 149, "src": "1453:6:0", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 172, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1462:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 171, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1462:6:0", "typeDescriptions": {}}}], "id": 173, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1461:8:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}], "expression": {"id": 168, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "1442:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 169, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "1446:6:0", "memberName": "decode", "nodeType": "MemberAccess", "src": "1442:10:0", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 174, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1442:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "1424:46:0"}, {"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 181, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 178, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 167, "src": "1492:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 177, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1487:4:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 176, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1487:4:0", "typeDescriptions": {}}}, "id": 179, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1487:14:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"hexValue": "3130", "id": 180, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1504:2:0", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "src": "1487:19:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 145, "id": 182, "nodeType": "Return", "src": "1480:26:0"}]}, "functionSelector": "da26663a", "id": 184, "implemented": true, "kind": "function", "modifiers": [], "name": "getPrice", "nameLocation": "1165:8:0", "nodeType": "FunctionDefinition", "parameters": {"id": 142, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 141, "mutability": "mutable", "name": "assetIndex", "nameLocation": "1181:10:0", "nodeType": "VariableDeclaration", "scope": 184, "src": "1174:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 140, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "1174:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "1173:19:0"}, "returnParameters": {"id": 145, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 144, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 184, "src": "1216:4:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 143, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1216:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1215:6:0"}, "scope": 185, "src": "1156:384:0", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 186, "src": "65:1477:0", "usedErrors": [], "usedEvents": []}], "src": "39:1503:0"}, "id": 0}}, "contracts": {"contracts/Please.sol": {"Please": {"abi": [{"inputs": [], "name": "findWorkingAssets", "outputs": [{"internalType": "uint32[]", "name": "workingIndices", "type": "uint32[]"}, {"internalType": "uint256[]", "name": "rawPrices", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "assetIndex", "type": "uint32"}], "name": "getPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH1 0xF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0x9FD DUP1 PUSH2 0x1F PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x36 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x30320DD4 EQ PUSH2 0x3B JUMPI DUP1 PUSH4 0xDA26663A EQ PUSH2 0x5A JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x43 PUSH2 0x8A JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x51 SWAP3 SWAP2 SWAP1 PUSH2 0x637 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x74 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x6F SWAP2 SWAP1 PUSH2 0x69F JUMP JUMPDEST PUSH2 0x398 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x81 SWAP2 SWAP1 PUSH2 0x6DB JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x0 PUSH1 0x14 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0xAA JUMPI PUSH2 0xA9 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0xD8 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP1 POP PUSH1 0x0 PUSH1 0x14 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0xF8 JUMPI PUSH2 0xF7 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x126 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP1 POP PUSH1 0x0 DUP1 JUMPDEST PUSH1 0x14 DUP2 PUSH4 0xFFFFFFFF AND LT ISZERO PUSH2 0x25F JUMPI PUSH1 0x0 DUP1 PUSH2 0x807 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP4 PUSH1 0x40 MLOAD PUSH1 0x20 ADD PUSH2 0x169 SWAP2 SWAP1 PUSH2 0x734 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 DUP4 SUB SUB DUP2 MSTORE SWAP1 PUSH1 0x40 MSTORE PUSH1 0x40 MLOAD PUSH2 0x185 SWAP2 SWAP1 PUSH2 0x7C0 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 GAS STATICCALL SWAP2 POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x1C0 JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x1C5 JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP SWAP2 POP SWAP2 POP DUP2 ISZERO PUSH2 0x250 JUMPI PUSH1 0x0 DUP2 DUP1 PUSH1 0x20 ADD SWAP1 MLOAD DUP2 ADD SWAP1 PUSH2 0x1E6 SWAP2 SWAP1 PUSH2 0x817 JUMP JUMPDEST SWAP1 POP DUP4 DUP8 DUP7 DUP2 MLOAD DUP2 LT PUSH2 0x1FC JUMPI PUSH2 0x1FB PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD SWAP1 PUSH4 0xFFFFFFFF AND SWAP1 DUP2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP7 DUP7 DUP2 MLOAD DUP2 LT PUSH2 0x234 JUMPI PUSH2 0x233 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD DUP2 DUP2 MSTORE POP POP DUP5 DUP1 PUSH2 0x24B SWAP1 PUSH2 0x8A2 JUMP JUMPDEST SWAP6 POP POP POP JUMPDEST POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0x12D JUMP JUMPDEST POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x27A JUMPI PUSH2 0x279 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x2A8 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP5 POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x2C5 JUMPI PUSH2 0x2C4 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x2F3 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP4 POP PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x390 JUMPI DUP4 DUP2 DUP2 MLOAD DUP2 LT PUSH2 0x314 JUMPI PUSH2 0x313 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD DUP7 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x32F JUMPI PUSH2 0x32E PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD SWAP1 PUSH4 0xFFFFFFFF AND SWAP1 DUP2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP POP DUP3 DUP2 DUP2 MLOAD DUP2 LT PUSH2 0x35C JUMPI PUSH2 0x35B PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD DUP6 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x377 JUMPI PUSH2 0x376 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD DUP2 DUP2 MSTORE POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0x2F9 JUMP JUMPDEST POP POP POP POP SWAP1 SWAP2 JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x0 PUSH2 0x807 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP5 PUSH1 0x40 MLOAD PUSH1 0x20 ADD PUSH2 0x3C7 SWAP2 SWAP1 PUSH2 0x734 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 DUP4 SUB SUB DUP2 MSTORE SWAP1 PUSH1 0x40 MSTORE PUSH1 0x40 MLOAD PUSH2 0x3E3 SWAP2 SWAP1 PUSH2 0x7C0 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 GAS STATICCALL SWAP2 POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x41E JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x423 JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP SWAP2 POP SWAP2 POP DUP2 PUSH2 0x468 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x45F SWAP1 PUSH2 0x947 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP2 DUP1 PUSH1 0x20 ADD SWAP1 MLOAD DUP2 ADD SWAP1 PUSH2 0x47E SWAP2 SWAP1 PUSH2 0x817 JUMP JUMPDEST SWAP1 POP PUSH1 0xA DUP2 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH2 0x497 SWAP2 SWAP1 PUSH2 0x996 JUMP JUMPDEST SWAP4 POP POP POP POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH4 0xFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x4E6 DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x4F8 DUP4 DUP4 PUSH2 0x4DD JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x51C DUP3 PUSH2 0x4A1 JUMP JUMPDEST PUSH2 0x526 DUP2 DUP6 PUSH2 0x4AC JUMP JUMPDEST SWAP4 POP PUSH2 0x531 DUP4 PUSH2 0x4BD JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x562 JUMPI DUP2 MLOAD PUSH2 0x549 DUP9 DUP3 PUSH2 0x4EC JUMP JUMPDEST SWAP8 POP PUSH2 0x554 DUP4 PUSH2 0x504 JUMP JUMPDEST SWAP3 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x535 JUMP JUMPDEST POP DUP6 SWAP4 POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x5AE DUP2 PUSH2 0x59B JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x5C0 DUP4 DUP4 PUSH2 0x5A5 JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x5E4 DUP3 PUSH2 0x56F JUMP JUMPDEST PUSH2 0x5EE DUP2 DUP6 PUSH2 0x57A JUMP JUMPDEST SWAP4 POP PUSH2 0x5F9 DUP4 PUSH2 0x58B JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x62A JUMPI DUP2 MLOAD PUSH2 0x611 DUP9 DUP3 PUSH2 0x5B4 JUMP JUMPDEST SWAP8 POP PUSH2 0x61C DUP4 PUSH2 0x5CC JUMP JUMPDEST SWAP3 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x5FD JUMP JUMPDEST POP DUP6 SWAP4 POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x651 DUP2 DUP6 PUSH2 0x511 JUMP JUMPDEST SWAP1 POP DUP2 DUP2 SUB PUSH1 0x20 DUP4 ADD MSTORE PUSH2 0x665 DUP2 DUP5 PUSH2 0x5D9 JUMP JUMPDEST SWAP1 POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x67C DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP2 EQ PUSH2 0x687 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x699 DUP2 PUSH2 0x673 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x6B5 JUMPI PUSH2 0x6B4 PUSH2 0x66E JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x6C3 DUP5 DUP3 DUP6 ADD PUSH2 0x68A JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x6D5 DUP2 PUSH2 0x59B JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x6F0 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x6CC JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x72E DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x749 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x725 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x783 JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x768 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x79A DUP3 PUSH2 0x74F JUMP JUMPDEST PUSH2 0x7A4 DUP2 DUP6 PUSH2 0x75A JUMP JUMPDEST SWAP4 POP PUSH2 0x7B4 DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x765 JUMP JUMPDEST DUP1 DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7CC DUP3 DUP5 PUSH2 0x78F JUMP JUMPDEST SWAP2 POP DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x7F4 DUP2 PUSH2 0x7D7 JUMP JUMPDEST DUP2 EQ PUSH2 0x7FF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0x811 DUP2 PUSH2 0x7EB JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x82D JUMPI PUSH2 0x82C PUSH2 0x66E JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x83B DUP5 DUP3 DUP6 ADD PUSH2 0x802 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0x8AD DUP3 PUSH2 0x59B JUMP JUMPDEST SWAP2 POP PUSH32 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 SUB PUSH2 0x8DF JUMPI PUSH2 0x8DE PUSH2 0x873 JUMP JUMPDEST JUMPDEST PUSH1 0x1 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x417373657420646F65736E277420657869737400000000000000000000000000 PUSH1 0x0 DUP3 ADD MSTORE POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x931 PUSH1 0x13 DUP4 PUSH2 0x8EA JUMP JUMPDEST SWAP2 POP PUSH2 0x93C DUP3 PUSH2 0x8FB JUMP JUMPDEST PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x960 DUP2 PUSH2 0x924 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x12 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0x9A1 DUP3 PUSH2 0x59B JUMP JUMPDEST SWAP2 POP PUSH2 0x9AC DUP4 PUSH2 0x59B JUMP JUMPDEST SWAP3 POP DUP3 PUSH2 0x9BC JUMPI PUSH2 0x9BB PUSH2 0x967 JUMP JUMPDEST JUMPDEST DUP3 DUP3 DIV SWAP1 POP SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH1 0x2A PUSH16 0x8538315CCA6449E49F19449D477B21D9 0xCE PUSH16 0x2F5B31796BF23AEE3A6A1064736F6C63 NUMBER STOP ADDMOD SHR STOP CALLER ", "sourceMap": "65:1477:0:-:0;;;;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@findWorkingAssets_139": {"entryPoint": 138, "id": 139, "parameterSlots": 0, "returnSlots": 2}, "@getPrice_184": {"entryPoint": 920, "id": 184, "parameterSlots": 1, "returnSlots": 1}, "abi_decode_t_uint32": {"entryPoint": 1674, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_t_uint64_fromMemory": {"entryPoint": 2050, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint32": {"entryPoint": 1695, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint64_fromMemory": {"entryPoint": 2071, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encodeUpdatedPos_t_uint256_to_t_uint256": {"entryPoint": 1460, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encodeUpdatedPos_t_uint32_to_t_uint32": {"entryPoint": 1260, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_array$_t_uint256_$dyn_memory_ptr_to_t_array$_t_uint256_$dyn_memory_ptr_fromStack": {"entryPoint": 1497, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_array$_t_uint32_$dyn_memory_ptr_to_t_array$_t_uint32_$dyn_memory_ptr_fromStack": {"entryPoint": 1297, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_bytes_memory_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack": {"entryPoint": 1935, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc_to_t_string_memory_ptr_fromStack": {"entryPoint": 2340, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_t_uint256_to_t_uint256": {"entryPoint": 1445, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint256_to_t_uint256_fromStack": {"entryPoint": 1740, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint32_to_t_uint32": {"entryPoint": 1245, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint32_to_t_uint32_fromStack": {"entryPoint": 1829, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_tuple_packed_t_bytes_memory_ptr__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed": {"entryPoint": 1984, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed": {"entryPoint": 1591, "id": null, "parameterSlots": 3, "returnSlots": 1}, "abi_encode_tuple_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc__to_t_string_memory_ptr__fromStack_reversed": {"entryPoint": 2375, "id": null, "parameterSlots": 1, "returnSlots": 1}, "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed": {"entryPoint": 1755, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed": {"entryPoint": 1844, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_unbounded": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 1}, "array_dataslot_t_array$_t_uint256_$dyn_memory_ptr": {"entryPoint": 1419, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_dataslot_t_array$_t_uint32_$dyn_memory_ptr": {"entryPoint": 1213, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_array$_t_uint256_$dyn_memory_ptr": {"entryPoint": 1391, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_array$_t_uint32_$dyn_memory_ptr": {"entryPoint": 1185, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_bytes_memory_ptr": {"entryPoint": 1871, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_nextElement_t_array$_t_uint256_$dyn_memory_ptr": {"entryPoint": 1484, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_nextElement_t_array$_t_uint32_$dyn_memory_ptr": {"entryPoint": 1284, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_storeLengthForEncoding_t_array$_t_uint256_$dyn_memory_ptr_fromStack": {"entryPoint": 1402, "id": null, "parameterSlots": 2, "returnSlots": 1}, "array_storeLengthForEncoding_t_array$_t_uint32_$dyn_memory_ptr_fromStack": {"entryPoint": 1196, "id": null, "parameterSlots": 2, "returnSlots": 1}, "array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack": {"entryPoint": 1882, "id": null, "parameterSlots": 2, "returnSlots": 1}, "array_storeLengthForEncoding_t_string_memory_ptr_fromStack": {"entryPoint": 2282, "id": null, "parameterSlots": 2, "returnSlots": 1}, "checked_div_t_uint256": {"entryPoint": 2454, "id": null, "parameterSlots": 2, "returnSlots": 1}, "cleanup_t_uint256": {"entryPoint": 1435, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint32": {"entryPoint": 1229, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint64": {"entryPoint": 2007, "id": null, "parameterSlots": 1, "returnSlots": 1}, "copy_memory_to_memory_with_cleanup": {"entryPoint": 1893, "id": null, "parameterSlots": 3, "returnSlots": 0}, "increment_t_uint256": {"entryPoint": 2210, "id": null, "parameterSlots": 1, "returnSlots": 1}, "panic_error_0x11": {"entryPoint": 2163, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x12": {"entryPoint": 2407, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x32": {"entryPoint": 2116, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x41": {"entryPoint": 1782, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b": {"entryPoint": 1646, "id": null, "parameterSlots": 0, "returnSlots": 0}, "store_literal_in_memory_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc": {"entryPoint": 2299, "id": null, "parameterSlots": 1, "returnSlots": 0}, "validator_revert_t_uint32": {"entryPoint": 1651, "id": null, "parameterSlots": 1, "returnSlots": 0}, "validator_revert_t_uint64": {"entryPoint": 2027, "id": null, "parameterSlots": 1, "returnSlots": 0}}, "generatedSources": [{"ast": {"nativeSrc": "0:9931:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:9931:1", "statements": [{"body": {"nativeSrc": "80:40:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "80:40:1", "statements": [{"nativeSrc": "91:22:1", "nodeType": "YulAssignment", "src": "91:22:1", "value": {"arguments": [{"name": "value", "nativeSrc": "107:5:1", "nodeType": "YulIdentifier", "src": "107:5:1"}], "functionName": {"name": "mload", "nativeSrc": "101:5:1", "nodeType": "YulIdentifier", "src": "101:5:1"}, "nativeSrc": "101:12:1", "nodeType": "YulFunctionCall", "src": "101:12:1"}, "variableNames": [{"name": "length", "nativeSrc": "91:6:1", "nodeType": "YulIdentifier", "src": "91:6:1"}]}]}, "name": "array_length_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "7:113:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "63:5:1", "nodeType": "YulTypedName", "src": "63:5:1", "type": ""}], "returnVariables": [{"name": "length", "nativeSrc": "73:6:1", "nodeType": "YulTypedName", "src": "73:6:1", "type": ""}], "src": "7:113:1"}, {"body": {"nativeSrc": "236:73:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "236:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "253:3:1", "nodeType": "YulIdentifier", "src": "253:3:1"}, {"name": "length", "nativeSrc": "258:6:1", "nodeType": "YulIdentifier", "src": "258:6:1"}], "functionName": {"name": "mstore", "nativeSrc": "246:6:1", "nodeType": "YulIdentifier", "src": "246:6:1"}, "nativeSrc": "246:19:1", "nodeType": "YulFunctionCall", "src": "246:19:1"}, "nativeSrc": "246:19:1", "nodeType": "YulExpressionStatement", "src": "246:19:1"}, {"nativeSrc": "274:29:1", "nodeType": "YulAssignment", "src": "274:29:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "293:3:1", "nodeType": "YulIdentifier", "src": "293:3:1"}, {"kind": "number", "nativeSrc": "298:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "298:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "289:3:1", "nodeType": "YulIdentifier", "src": "289:3:1"}, "nativeSrc": "289:14:1", "nodeType": "YulFunctionCall", "src": "289:14:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "274:11:1", "nodeType": "YulIdentifier", "src": "274:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_array$_t_uint32_$dyn_memory_ptr_fromStack", "nativeSrc": "126:183:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "208:3:1", "nodeType": "YulTypedName", "src": "208:3:1", "type": ""}, {"name": "length", "nativeSrc": "213:6:1", "nodeType": "YulTypedName", "src": "213:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "224:11:1", "nodeType": "YulTypedName", "src": "224:11:1", "type": ""}], "src": "126:183:1"}, {"body": {"nativeSrc": "386:60:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "386:60:1", "statements": [{"nativeSrc": "396:11:1", "nodeType": "YulAssignment", "src": "396:11:1", "value": {"name": "ptr", "nativeSrc": "404:3:1", "nodeType": "YulIdentifier", "src": "404:3:1"}, "variableNames": [{"name": "data", "nativeSrc": "396:4:1", "nodeType": "YulIdentifier", "src": "396:4:1"}]}, {"nativeSrc": "417:22:1", "nodeType": "YulAssignment", "src": "417:22:1", "value": {"arguments": [{"name": "ptr", "nativeSrc": "429:3:1", "nodeType": "YulIdentifier", "src": "429:3:1"}, {"kind": "number", "nativeSrc": "434:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "434:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "425:3:1", "nodeType": "YulIdentifier", "src": "425:3:1"}, "nativeSrc": "425:14:1", "nodeType": "YulFunctionCall", "src": "425:14:1"}, "variableNames": [{"name": "data", "nativeSrc": "417:4:1", "nodeType": "YulIdentifier", "src": "417:4:1"}]}]}, "name": "array_dataslot_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "315:131:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "373:3:1", "nodeType": "YulTypedName", "src": "373:3:1", "type": ""}], "returnVariables": [{"name": "data", "nativeSrc": "381:4:1", "nodeType": "YulTypedName", "src": "381:4:1", "type": ""}], "src": "315:131:1"}, {"body": {"nativeSrc": "496:49:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "496:49:1", "statements": [{"nativeSrc": "506:33:1", "nodeType": "YulAssignment", "src": "506:33:1", "value": {"arguments": [{"name": "value", "nativeSrc": "521:5:1", "nodeType": "YulIdentifier", "src": "521:5:1"}, {"kind": "number", "nativeSrc": "528:10:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "528:10:1", "type": "", "value": "0xffffffff"}], "functionName": {"name": "and", "nativeSrc": "517:3:1", "nodeType": "YulIdentifier", "src": "517:3:1"}, "nativeSrc": "517:22:1", "nodeType": "YulFunctionCall", "src": "517:22:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "506:7:1", "nodeType": "YulIdentifier", "src": "506:7:1"}]}]}, "name": "cleanup_t_uint32", "nativeSrc": "452:93:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "478:5:1", "nodeType": "YulTypedName", "src": "478:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "488:7:1", "nodeType": "YulTypedName", "src": "488:7:1", "type": ""}], "src": "452:93:1"}, {"body": {"nativeSrc": "604:52:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "604:52:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "621:3:1", "nodeType": "YulIdentifier", "src": "621:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "643:5:1", "nodeType": "YulIdentifier", "src": "643:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "626:16:1", "nodeType": "YulIdentifier", "src": "626:16:1"}, "nativeSrc": "626:23:1", "nodeType": "YulFunctionCall", "src": "626:23:1"}], "functionName": {"name": "mstore", "nativeSrc": "614:6:1", "nodeType": "YulIdentifier", "src": "614:6:1"}, "nativeSrc": "614:36:1", "nodeType": "YulFunctionCall", "src": "614:36:1"}, "nativeSrc": "614:36:1", "nodeType": "YulExpressionStatement", "src": "614:36:1"}]}, "name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "551:105:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "592:5:1", "nodeType": "YulTypedName", "src": "592:5:1", "type": ""}, {"name": "pos", "nativeSrc": "599:3:1", "nodeType": "YulTypedName", "src": "599:3:1", "type": ""}], "src": "551:105:1"}, {"body": {"nativeSrc": "740:97:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "740:97:1", "statements": [{"expression": {"arguments": [{"name": "value0", "nativeSrc": "782:6:1", "nodeType": "YulIdentifier", "src": "782:6:1"}, {"name": "pos", "nativeSrc": "790:3:1", "nodeType": "YulIdentifier", "src": "790:3:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "750:31:1", "nodeType": "YulIdentifier", "src": "750:31:1"}, "nativeSrc": "750:44:1", "nodeType": "YulFunctionCall", "src": "750:44:1"}, "nativeSrc": "750:44:1", "nodeType": "YulExpressionStatement", "src": "750:44:1"}, {"nativeSrc": "803:28:1", "nodeType": "YulAssignment", "src": "803:28:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "821:3:1", "nodeType": "YulIdentifier", "src": "821:3:1"}, {"kind": "number", "nativeSrc": "826:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "826:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "817:3:1", "nodeType": "YulIdentifier", "src": "817:3:1"}, "nativeSrc": "817:14:1", "nodeType": "YulFunctionCall", "src": "817:14:1"}, "variableNames": [{"name": "updatedPos", "nativeSrc": "803:10:1", "nodeType": "YulIdentifier", "src": "803:10:1"}]}]}, "name": "abi_encodeUpdatedPos_t_uint32_to_t_uint32", "nativeSrc": "662:175:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value0", "nativeSrc": "713:6:1", "nodeType": "YulTypedName", "src": "713:6:1", "type": ""}, {"name": "pos", "nativeSrc": "721:3:1", "nodeType": "YulTypedName", "src": "721:3:1", "type": ""}], "returnVariables": [{"name": "updatedPos", "nativeSrc": "729:10:1", "nodeType": "YulTypedName", "src": "729:10:1", "type": ""}], "src": "662:175:1"}, {"body": {"nativeSrc": "917:38:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "917:38:1", "statements": [{"nativeSrc": "927:22:1", "nodeType": "YulAssignment", "src": "927:22:1", "value": {"arguments": [{"name": "ptr", "nativeSrc": "939:3:1", "nodeType": "YulIdentifier", "src": "939:3:1"}, {"kind": "number", "nativeSrc": "944:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "944:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "935:3:1", "nodeType": "YulIdentifier", "src": "935:3:1"}, "nativeSrc": "935:14:1", "nodeType": "YulFunctionCall", "src": "935:14:1"}, "variableNames": [{"name": "next", "nativeSrc": "927:4:1", "nodeType": "YulIdentifier", "src": "927:4:1"}]}]}, "name": "array_nextElement_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "843:112:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "904:3:1", "nodeType": "YulTypedName", "src": "904:3:1", "type": ""}], "returnVariables": [{"name": "next", "nativeSrc": "912:4:1", "nodeType": "YulTypedName", "src": "912:4:1", "type": ""}], "src": "843:112:1"}, {"body": {"nativeSrc": "1111:602:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1111:602:1", "statements": [{"nativeSrc": "1121:67:1", "nodeType": "YulVariableDeclaration", "src": "1121:67:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1182:5:1", "nodeType": "YulIdentifier", "src": "1182:5:1"}], "functionName": {"name": "array_length_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "1135:46:1", "nodeType": "YulIdentifier", "src": "1135:46:1"}, "nativeSrc": "1135:53:1", "nodeType": "YulFunctionCall", "src": "1135:53:1"}, "variables": [{"name": "length", "nativeSrc": "1125:6:1", "nodeType": "YulTypedName", "src": "1125:6:1", "type": ""}]}, {"nativeSrc": "1197:92:1", "nodeType": "YulAssignment", "src": "1197:92:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "1277:3:1", "nodeType": "YulIdentifier", "src": "1277:3:1"}, {"name": "length", "nativeSrc": "1282:6:1", "nodeType": "YulIdentifier", "src": "1282:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_array$_t_uint32_$dyn_memory_ptr_fromStack", "nativeSrc": "1204:72:1", "nodeType": "YulIdentifier", "src": "1204:72:1"}, "nativeSrc": "1204:85:1", "nodeType": "YulFunctionCall", "src": "1204:85:1"}, "variableNames": [{"name": "pos", "nativeSrc": "1197:3:1", "nodeType": "YulIdentifier", "src": "1197:3:1"}]}, {"nativeSrc": "1298:70:1", "nodeType": "YulVariableDeclaration", "src": "1298:70:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1362:5:1", "nodeType": "YulIdentifier", "src": "1362:5:1"}], "functionName": {"name": "array_dataslot_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "1313:48:1", "nodeType": "YulIdentifier", "src": "1313:48:1"}, "nativeSrc": "1313:55:1", "nodeType": "YulFunctionCall", "src": "1313:55:1"}, "variables": [{"name": "baseRef", "nativeSrc": "1302:7:1", "nodeType": "YulTypedName", "src": "1302:7:1", "type": ""}]}, {"nativeSrc": "1377:21:1", "nodeType": "YulVariableDeclaration", "src": "1377:21:1", "value": {"name": "baseRef", "nativeSrc": "1391:7:1", "nodeType": "YulIdentifier", "src": "1391:7:1"}, "variables": [{"name": "srcPtr", "nativeSrc": "1381:6:1", "nodeType": "YulTypedName", "src": "1381:6:1", "type": ""}]}, {"body": {"nativeSrc": "1467:221:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1467:221:1", "statements": [{"nativeSrc": "1481:34:1", "nodeType": "YulVariableDeclaration", "src": "1481:34:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "1508:6:1", "nodeType": "YulIdentifier", "src": "1508:6:1"}], "functionName": {"name": "mload", "nativeSrc": "1502:5:1", "nodeType": "YulIdentifier", "src": "1502:5:1"}, "nativeSrc": "1502:13:1", "nodeType": "YulFunctionCall", "src": "1502:13:1"}, "variables": [{"name": "elementValue0", "nativeSrc": "1485:13:1", "nodeType": "YulTypedName", "src": "1485:13:1", "type": ""}]}, {"nativeSrc": "1528:68:1", "nodeType": "YulAssignment", "src": "1528:68:1", "value": {"arguments": [{"name": "elementValue0", "nativeSrc": "1577:13:1", "nodeType": "YulIdentifier", "src": "1577:13:1"}, {"name": "pos", "nativeSrc": "1592:3:1", "nodeType": "YulIdentifier", "src": "1592:3:1"}], "functionName": {"name": "abi_encodeUpdatedPos_t_uint32_to_t_uint32", "nativeSrc": "1535:41:1", "nodeType": "YulIdentifier", "src": "1535:41:1"}, "nativeSrc": "1535:61:1", "nodeType": "YulFunctionCall", "src": "1535:61:1"}, "variableNames": [{"name": "pos", "nativeSrc": "1528:3:1", "nodeType": "YulIdentifier", "src": "1528:3:1"}]}, {"nativeSrc": "1609:69:1", "nodeType": "YulAssignment", "src": "1609:69:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "1671:6:1", "nodeType": "YulIdentifier", "src": "1671:6:1"}], "functionName": {"name": "array_nextElement_t_array$_t_uint32_$dyn_memory_ptr", "nativeSrc": "1619:51:1", "nodeType": "YulIdentifier", "src": "1619:51:1"}, "nativeSrc": "1619:59:1", "nodeType": "YulFunctionCall", "src": "1619:59:1"}, "variableNames": [{"name": "srcPtr", "nativeSrc": "1609:6:1", "nodeType": "YulIdentifier", "src": "1609:6:1"}]}]}, "condition": {"arguments": [{"name": "i", "nativeSrc": "1429:1:1", "nodeType": "YulIdentifier", "src": "1429:1:1"}, {"name": "length", "nativeSrc": "1432:6:1", "nodeType": "YulIdentifier", "src": "1432:6:1"}], "functionName": {"name": "lt", "nativeSrc": "1426:2:1", "nodeType": "YulIdentifier", "src": "1426:2:1"}, "nativeSrc": "1426:13:1", "nodeType": "YulFunctionCall", "src": "1426:13:1"}, "nativeSrc": "1407:281:1", "nodeType": "YulForLoop", "post": {"nativeSrc": "1440:18:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1440:18:1", "statements": [{"nativeSrc": "1442:14:1", "nodeType": "YulAssignment", "src": "1442:14:1", "value": {"arguments": [{"name": "i", "nativeSrc": "1451:1:1", "nodeType": "YulIdentifier", "src": "1451:1:1"}, {"kind": "number", "nativeSrc": "1454:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1454:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nativeSrc": "1447:3:1", "nodeType": "YulIdentifier", "src": "1447:3:1"}, "nativeSrc": "1447:9:1", "nodeType": "YulFunctionCall", "src": "1447:9:1"}, "variableNames": [{"name": "i", "nativeSrc": "1442:1:1", "nodeType": "YulIdentifier", "src": "1442:1:1"}]}]}, "pre": {"nativeSrc": "1411:14:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1411:14:1", "statements": [{"nativeSrc": "1413:10:1", "nodeType": "YulVariableDeclaration", "src": "1413:10:1", "value": {"kind": "number", "nativeSrc": "1422:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1422:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nativeSrc": "1417:1:1", "nodeType": "YulTypedName", "src": "1417:1:1", "type": ""}]}]}, "src": "1407:281:1"}, {"nativeSrc": "1697:10:1", "nodeType": "YulAssignment", "src": "1697:10:1", "value": {"name": "pos", "nativeSrc": "1704:3:1", "nodeType": "YulIdentifier", "src": "1704:3:1"}, "variableNames": [{"name": "end", "nativeSrc": "1697:3:1", "nodeType": "YulIdentifier", "src": "1697:3:1"}]}]}, "name": "abi_encode_t_array$_t_uint32_$dyn_memory_ptr_to_t_array$_t_uint32_$dyn_memory_ptr_fromStack", "nativeSrc": "989:724:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1090:5:1", "nodeType": "YulTypedName", "src": "1090:5:1", "type": ""}, {"name": "pos", "nativeSrc": "1097:3:1", "nodeType": "YulTypedName", "src": "1097:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "1106:3:1", "nodeType": "YulTypedName", "src": "1106:3:1", "type": ""}], "src": "989:724:1"}, {"body": {"nativeSrc": "1793:40:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1793:40:1", "statements": [{"nativeSrc": "1804:22:1", "nodeType": "YulAssignment", "src": "1804:22:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1820:5:1", "nodeType": "YulIdentifier", "src": "1820:5:1"}], "functionName": {"name": "mload", "nativeSrc": "1814:5:1", "nodeType": "YulIdentifier", "src": "1814:5:1"}, "nativeSrc": "1814:12:1", "nodeType": "YulFunctionCall", "src": "1814:12:1"}, "variableNames": [{"name": "length", "nativeSrc": "1804:6:1", "nodeType": "YulIdentifier", "src": "1804:6:1"}]}]}, "name": "array_length_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "1719:114:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1776:5:1", "nodeType": "YulTypedName", "src": "1776:5:1", "type": ""}], "returnVariables": [{"name": "length", "nativeSrc": "1786:6:1", "nodeType": "YulTypedName", "src": "1786:6:1", "type": ""}], "src": "1719:114:1"}, {"body": {"nativeSrc": "1950:73:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1950:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "1967:3:1", "nodeType": "YulIdentifier", "src": "1967:3:1"}, {"name": "length", "nativeSrc": "1972:6:1", "nodeType": "YulIdentifier", "src": "1972:6:1"}], "functionName": {"name": "mstore", "nativeSrc": "1960:6:1", "nodeType": "YulIdentifier", "src": "1960:6:1"}, "nativeSrc": "1960:19:1", "nodeType": "YulFunctionCall", "src": "1960:19:1"}, "nativeSrc": "1960:19:1", "nodeType": "YulExpressionStatement", "src": "1960:19:1"}, {"nativeSrc": "1988:29:1", "nodeType": "YulAssignment", "src": "1988:29:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "2007:3:1", "nodeType": "YulIdentifier", "src": "2007:3:1"}, {"kind": "number", "nativeSrc": "2012:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2012:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2003:3:1", "nodeType": "YulIdentifier", "src": "2003:3:1"}, "nativeSrc": "2003:14:1", "nodeType": "YulFunctionCall", "src": "2003:14:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "1988:11:1", "nodeType": "YulIdentifier", "src": "1988:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_array$_t_uint256_$dyn_memory_ptr_fromStack", "nativeSrc": "1839:184:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "1922:3:1", "nodeType": "YulTypedName", "src": "1922:3:1", "type": ""}, {"name": "length", "nativeSrc": "1927:6:1", "nodeType": "YulTypedName", "src": "1927:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "1938:11:1", "nodeType": "YulTypedName", "src": "1938:11:1", "type": ""}], "src": "1839:184:1"}, {"body": {"nativeSrc": "2101:60:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2101:60:1", "statements": [{"nativeSrc": "2111:11:1", "nodeType": "YulAssignment", "src": "2111:11:1", "value": {"name": "ptr", "nativeSrc": "2119:3:1", "nodeType": "YulIdentifier", "src": "2119:3:1"}, "variableNames": [{"name": "data", "nativeSrc": "2111:4:1", "nodeType": "YulIdentifier", "src": "2111:4:1"}]}, {"nativeSrc": "2132:22:1", "nodeType": "YulAssignment", "src": "2132:22:1", "value": {"arguments": [{"name": "ptr", "nativeSrc": "2144:3:1", "nodeType": "YulIdentifier", "src": "2144:3:1"}, {"kind": "number", "nativeSrc": "2149:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2149:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2140:3:1", "nodeType": "YulIdentifier", "src": "2140:3:1"}, "nativeSrc": "2140:14:1", "nodeType": "YulFunctionCall", "src": "2140:14:1"}, "variableNames": [{"name": "data", "nativeSrc": "2132:4:1", "nodeType": "YulIdentifier", "src": "2132:4:1"}]}]}, "name": "array_dataslot_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "2029:132:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "2088:3:1", "nodeType": "YulTypedName", "src": "2088:3:1", "type": ""}], "returnVariables": [{"name": "data", "nativeSrc": "2096:4:1", "nodeType": "YulTypedName", "src": "2096:4:1", "type": ""}], "src": "2029:132:1"}, {"body": {"nativeSrc": "2212:32:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2212:32:1", "statements": [{"nativeSrc": "2222:16:1", "nodeType": "YulAssignment", "src": "2222:16:1", "value": {"name": "value", "nativeSrc": "2233:5:1", "nodeType": "YulIdentifier", "src": "2233:5:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "2222:7:1", "nodeType": "YulIdentifier", "src": "2222:7:1"}]}]}, "name": "cleanup_t_uint256", "nativeSrc": "2167:77:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "2194:5:1", "nodeType": "YulTypedName", "src": "2194:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "2204:7:1", "nodeType": "YulTypedName", "src": "2204:7:1", "type": ""}], "src": "2167:77:1"}, {"body": {"nativeSrc": "2305:53:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2305:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "2322:3:1", "nodeType": "YulIdentifier", "src": "2322:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "2345:5:1", "nodeType": "YulIdentifier", "src": "2345:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2327:17:1", "nodeType": "YulIdentifier", "src": "2327:17:1"}, "nativeSrc": "2327:24:1", "nodeType": "YulFunctionCall", "src": "2327:24:1"}], "functionName": {"name": "mstore", "nativeSrc": "2315:6:1", "nodeType": "YulIdentifier", "src": "2315:6:1"}, "nativeSrc": "2315:37:1", "nodeType": "YulFunctionCall", "src": "2315:37:1"}, "nativeSrc": "2315:37:1", "nodeType": "YulExpressionStatement", "src": "2315:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256", "nativeSrc": "2250:108:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "2293:5:1", "nodeType": "YulTypedName", "src": "2293:5:1", "type": ""}, {"name": "pos", "nativeSrc": "2300:3:1", "nodeType": "YulTypedName", "src": "2300:3:1", "type": ""}], "src": "2250:108:1"}, {"body": {"nativeSrc": "2444:99:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2444:99:1", "statements": [{"expression": {"arguments": [{"name": "value0", "nativeSrc": "2488:6:1", "nodeType": "YulIdentifier", "src": "2488:6:1"}, {"name": "pos", "nativeSrc": "2496:3:1", "nodeType": "YulIdentifier", "src": "2496:3:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256", "nativeSrc": "2454:33:1", "nodeType": "YulIdentifier", "src": "2454:33:1"}, "nativeSrc": "2454:46:1", "nodeType": "YulFunctionCall", "src": "2454:46:1"}, "nativeSrc": "2454:46:1", "nodeType": "YulExpressionStatement", "src": "2454:46:1"}, {"nativeSrc": "2509:28:1", "nodeType": "YulAssignment", "src": "2509:28:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "2527:3:1", "nodeType": "YulIdentifier", "src": "2527:3:1"}, {"kind": "number", "nativeSrc": "2532:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2532:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2523:3:1", "nodeType": "YulIdentifier", "src": "2523:3:1"}, "nativeSrc": "2523:14:1", "nodeType": "YulFunctionCall", "src": "2523:14:1"}, "variableNames": [{"name": "updatedPos", "nativeSrc": "2509:10:1", "nodeType": "YulIdentifier", "src": "2509:10:1"}]}]}, "name": "abi_encodeUpdatedPos_t_uint256_to_t_uint256", "nativeSrc": "2364:179:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value0", "nativeSrc": "2417:6:1", "nodeType": "YulTypedName", "src": "2417:6:1", "type": ""}, {"name": "pos", "nativeSrc": "2425:3:1", "nodeType": "YulTypedName", "src": "2425:3:1", "type": ""}], "returnVariables": [{"name": "updatedPos", "nativeSrc": "2433:10:1", "nodeType": "YulTypedName", "src": "2433:10:1", "type": ""}], "src": "2364:179:1"}, {"body": {"nativeSrc": "2624:38:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2624:38:1", "statements": [{"nativeSrc": "2634:22:1", "nodeType": "YulAssignment", "src": "2634:22:1", "value": {"arguments": [{"name": "ptr", "nativeSrc": "2646:3:1", "nodeType": "YulIdentifier", "src": "2646:3:1"}, {"kind": "number", "nativeSrc": "2651:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2651:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2642:3:1", "nodeType": "YulIdentifier", "src": "2642:3:1"}, "nativeSrc": "2642:14:1", "nodeType": "YulFunctionCall", "src": "2642:14:1"}, "variableNames": [{"name": "next", "nativeSrc": "2634:4:1", "nodeType": "YulIdentifier", "src": "2634:4:1"}]}]}, "name": "array_nextElement_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "2549:113:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "2611:3:1", "nodeType": "YulTypedName", "src": "2611:3:1", "type": ""}], "returnVariables": [{"name": "next", "nativeSrc": "2619:4:1", "nodeType": "YulTypedName", "src": "2619:4:1", "type": ""}], "src": "2549:113:1"}, {"body": {"nativeSrc": "2822:608:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2822:608:1", "statements": [{"nativeSrc": "2832:68:1", "nodeType": "YulVariableDeclaration", "src": "2832:68:1", "value": {"arguments": [{"name": "value", "nativeSrc": "2894:5:1", "nodeType": "YulIdentifier", "src": "2894:5:1"}], "functionName": {"name": "array_length_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "2846:47:1", "nodeType": "YulIdentifier", "src": "2846:47:1"}, "nativeSrc": "2846:54:1", "nodeType": "YulFunctionCall", "src": "2846:54:1"}, "variables": [{"name": "length", "nativeSrc": "2836:6:1", "nodeType": "YulTypedName", "src": "2836:6:1", "type": ""}]}, {"nativeSrc": "2909:93:1", "nodeType": "YulAssignment", "src": "2909:93:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "2990:3:1", "nodeType": "YulIdentifier", "src": "2990:3:1"}, {"name": "length", "nativeSrc": "2995:6:1", "nodeType": "YulIdentifier", "src": "2995:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_array$_t_uint256_$dyn_memory_ptr_fromStack", "nativeSrc": "2916:73:1", "nodeType": "YulIdentifier", "src": "2916:73:1"}, "nativeSrc": "2916:86:1", "nodeType": "YulFunctionCall", "src": "2916:86:1"}, "variableNames": [{"name": "pos", "nativeSrc": "2909:3:1", "nodeType": "YulIdentifier", "src": "2909:3:1"}]}, {"nativeSrc": "3011:71:1", "nodeType": "YulVariableDeclaration", "src": "3011:71:1", "value": {"arguments": [{"name": "value", "nativeSrc": "3076:5:1", "nodeType": "YulIdentifier", "src": "3076:5:1"}], "functionName": {"name": "array_dataslot_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "3026:49:1", "nodeType": "YulIdentifier", "src": "3026:49:1"}, "nativeSrc": "3026:56:1", "nodeType": "YulFunctionCall", "src": "3026:56:1"}, "variables": [{"name": "baseRef", "nativeSrc": "3015:7:1", "nodeType": "YulTypedName", "src": "3015:7:1", "type": ""}]}, {"nativeSrc": "3091:21:1", "nodeType": "YulVariableDeclaration", "src": "3091:21:1", "value": {"name": "baseRef", "nativeSrc": "3105:7:1", "nodeType": "YulIdentifier", "src": "3105:7:1"}, "variables": [{"name": "srcPtr", "nativeSrc": "3095:6:1", "nodeType": "YulTypedName", "src": "3095:6:1", "type": ""}]}, {"body": {"nativeSrc": "3181:224:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3181:224:1", "statements": [{"nativeSrc": "3195:34:1", "nodeType": "YulVariableDeclaration", "src": "3195:34:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "3222:6:1", "nodeType": "YulIdentifier", "src": "3222:6:1"}], "functionName": {"name": "mload", "nativeSrc": "3216:5:1", "nodeType": "YulIdentifier", "src": "3216:5:1"}, "nativeSrc": "3216:13:1", "nodeType": "YulFunctionCall", "src": "3216:13:1"}, "variables": [{"name": "elementValue0", "nativeSrc": "3199:13:1", "nodeType": "YulTypedName", "src": "3199:13:1", "type": ""}]}, {"nativeSrc": "3242:70:1", "nodeType": "YulAssignment", "src": "3242:70:1", "value": {"arguments": [{"name": "elementValue0", "nativeSrc": "3293:13:1", "nodeType": "YulIdentifier", "src": "3293:13:1"}, {"name": "pos", "nativeSrc": "3308:3:1", "nodeType": "YulIdentifier", "src": "3308:3:1"}], "functionName": {"name": "abi_encodeUpdatedPos_t_uint256_to_t_uint256", "nativeSrc": "3249:43:1", "nodeType": "YulIdentifier", "src": "3249:43:1"}, "nativeSrc": "3249:63:1", "nodeType": "YulFunctionCall", "src": "3249:63:1"}, "variableNames": [{"name": "pos", "nativeSrc": "3242:3:1", "nodeType": "YulIdentifier", "src": "3242:3:1"}]}, {"nativeSrc": "3325:70:1", "nodeType": "YulAssignment", "src": "3325:70:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "3388:6:1", "nodeType": "YulIdentifier", "src": "3388:6:1"}], "functionName": {"name": "array_nextElement_t_array$_t_uint256_$dyn_memory_ptr", "nativeSrc": "3335:52:1", "nodeType": "YulIdentifier", "src": "3335:52:1"}, "nativeSrc": "3335:60:1", "nodeType": "YulFunctionCall", "src": "3335:60:1"}, "variableNames": [{"name": "srcPtr", "nativeSrc": "3325:6:1", "nodeType": "YulIdentifier", "src": "3325:6:1"}]}]}, "condition": {"arguments": [{"name": "i", "nativeSrc": "3143:1:1", "nodeType": "YulIdentifier", "src": "3143:1:1"}, {"name": "length", "nativeSrc": "3146:6:1", "nodeType": "YulIdentifier", "src": "3146:6:1"}], "functionName": {"name": "lt", "nativeSrc": "3140:2:1", "nodeType": "YulIdentifier", "src": "3140:2:1"}, "nativeSrc": "3140:13:1", "nodeType": "YulFunctionCall", "src": "3140:13:1"}, "nativeSrc": "3121:284:1", "nodeType": "YulForLoop", "post": {"nativeSrc": "3154:18:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3154:18:1", "statements": [{"nativeSrc": "3156:14:1", "nodeType": "YulAssignment", "src": "3156:14:1", "value": {"arguments": [{"name": "i", "nativeSrc": "3165:1:1", "nodeType": "YulIdentifier", "src": "3165:1:1"}, {"kind": "number", "nativeSrc": "3168:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3168:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nativeSrc": "3161:3:1", "nodeType": "YulIdentifier", "src": "3161:3:1"}, "nativeSrc": "3161:9:1", "nodeType": "YulFunctionCall", "src": "3161:9:1"}, "variableNames": [{"name": "i", "nativeSrc": "3156:1:1", "nodeType": "YulIdentifier", "src": "3156:1:1"}]}]}, "pre": {"nativeSrc": "3125:14:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3125:14:1", "statements": [{"nativeSrc": "3127:10:1", "nodeType": "YulVariableDeclaration", "src": "3127:10:1", "value": {"kind": "number", "nativeSrc": "3136:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3136:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nativeSrc": "3131:1:1", "nodeType": "YulTypedName", "src": "3131:1:1", "type": ""}]}]}, "src": "3121:284:1"}, {"nativeSrc": "3414:10:1", "nodeType": "YulAssignment", "src": "3414:10:1", "value": {"name": "pos", "nativeSrc": "3421:3:1", "nodeType": "YulIdentifier", "src": "3421:3:1"}, "variableNames": [{"name": "end", "nativeSrc": "3414:3:1", "nodeType": "YulIdentifier", "src": "3414:3:1"}]}]}, "name": "abi_encode_t_array$_t_uint256_$dyn_memory_ptr_to_t_array$_t_uint256_$dyn_memory_ptr_fromStack", "nativeSrc": "2698:732:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "2801:5:1", "nodeType": "YulTypedName", "src": "2801:5:1", "type": ""}, {"name": "pos", "nativeSrc": "2808:3:1", "nodeType": "YulTypedName", "src": "2808:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "2817:3:1", "nodeType": "YulTypedName", "src": "2817:3:1", "type": ""}], "src": "2698:732:1"}, {"body": {"nativeSrc": "3660:406:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3660:406:1", "statements": [{"nativeSrc": "3670:26:1", "nodeType": "YulAssignment", "src": "3670:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "3682:9:1", "nodeType": "YulIdentifier", "src": "3682:9:1"}, {"kind": "number", "nativeSrc": "3693:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3693:2:1", "type": "", "value": "64"}], "functionName": {"name": "add", "nativeSrc": "3678:3:1", "nodeType": "YulIdentifier", "src": "3678:3:1"}, "nativeSrc": "3678:18:1", "nodeType": "YulFunctionCall", "src": "3678:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "3670:4:1", "nodeType": "YulIdentifier", "src": "3670:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3717:9:1", "nodeType": "YulIdentifier", "src": "3717:9:1"}, {"kind": "number", "nativeSrc": "3728:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3728:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "3713:3:1", "nodeType": "YulIdentifier", "src": "3713:3:1"}, "nativeSrc": "3713:17:1", "nodeType": "YulFunctionCall", "src": "3713:17:1"}, {"arguments": [{"name": "tail", "nativeSrc": "3736:4:1", "nodeType": "YulIdentifier", "src": "3736:4:1"}, {"name": "headStart", "nativeSrc": "3742:9:1", "nodeType": "YulIdentifier", "src": "3742:9:1"}], "functionName": {"name": "sub", "nativeSrc": "3732:3:1", "nodeType": "YulIdentifier", "src": "3732:3:1"}, "nativeSrc": "3732:20:1", "nodeType": "YulFunctionCall", "src": "3732:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "3706:6:1", "nodeType": "YulIdentifier", "src": "3706:6:1"}, "nativeSrc": "3706:47:1", "nodeType": "YulFunctionCall", "src": "3706:47:1"}, "nativeSrc": "3706:47:1", "nodeType": "YulExpressionStatement", "src": "3706:47:1"}, {"nativeSrc": "3762:114:1", "nodeType": "YulAssignment", "src": "3762:114:1", "value": {"arguments": [{"name": "value0", "nativeSrc": "3862:6:1", "nodeType": "YulIdentifier", "src": "3862:6:1"}, {"name": "tail", "nativeSrc": "3871:4:1", "nodeType": "YulIdentifier", "src": "3871:4:1"}], "functionName": {"name": "abi_encode_t_array$_t_uint32_$dyn_memory_ptr_to_t_array$_t_uint32_$dyn_memory_ptr_fromStack", "nativeSrc": "3770:91:1", "nodeType": "YulIdentifier", "src": "3770:91:1"}, "nativeSrc": "3770:106:1", "nodeType": "YulFunctionCall", "src": "3770:106:1"}, "variableNames": [{"name": "tail", "nativeSrc": "3762:4:1", "nodeType": "YulIdentifier", "src": "3762:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "3897:9:1", "nodeType": "YulIdentifier", "src": "3897:9:1"}, {"kind": "number", "nativeSrc": "3908:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3908:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "3893:3:1", "nodeType": "YulIdentifier", "src": "3893:3:1"}, "nativeSrc": "3893:18:1", "nodeType": "YulFunctionCall", "src": "3893:18:1"}, {"arguments": [{"name": "tail", "nativeSrc": "3917:4:1", "nodeType": "YulIdentifier", "src": "3917:4:1"}, {"name": "headStart", "nativeSrc": "3923:9:1", "nodeType": "YulIdentifier", "src": "3923:9:1"}], "functionName": {"name": "sub", "nativeSrc": "3913:3:1", "nodeType": "YulIdentifier", "src": "3913:3:1"}, "nativeSrc": "3913:20:1", "nodeType": "YulFunctionCall", "src": "3913:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "3886:6:1", "nodeType": "YulIdentifier", "src": "3886:6:1"}, "nativeSrc": "3886:48:1", "nodeType": "YulFunctionCall", "src": "3886:48:1"}, "nativeSrc": "3886:48:1", "nodeType": "YulExpressionStatement", "src": "3886:48:1"}, {"nativeSrc": "3943:116:1", "nodeType": "YulAssignment", "src": "3943:116:1", "value": {"arguments": [{"name": "value1", "nativeSrc": "4045:6:1", "nodeType": "YulIdentifier", "src": "4045:6:1"}, {"name": "tail", "nativeSrc": "4054:4:1", "nodeType": "YulIdentifier", "src": "4054:4:1"}], "functionName": {"name": "abi_encode_t_array$_t_uint256_$dyn_memory_ptr_to_t_array$_t_uint256_$dyn_memory_ptr_fromStack", "nativeSrc": "3951:93:1", "nodeType": "YulIdentifier", "src": "3951:93:1"}, "nativeSrc": "3951:108:1", "nodeType": "YulFunctionCall", "src": "3951:108:1"}, "variableNames": [{"name": "tail", "nativeSrc": "3943:4:1", "nodeType": "YulIdentifier", "src": "3943:4:1"}]}]}, "name": "abi_encode_tuple_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed", "nativeSrc": "3436:630:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "3624:9:1", "nodeType": "YulTypedName", "src": "3624:9:1", "type": ""}, {"name": "value1", "nativeSrc": "3636:6:1", "nodeType": "YulTypedName", "src": "3636:6:1", "type": ""}, {"name": "value0", "nativeSrc": "3644:6:1", "nodeType": "YulTypedName", "src": "3644:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "3655:4:1", "nodeType": "YulTypedName", "src": "3655:4:1", "type": ""}], "src": "3436:630:1"}, {"body": {"nativeSrc": "4112:35:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4112:35:1", "statements": [{"nativeSrc": "4122:19:1", "nodeType": "YulAssignment", "src": "4122:19:1", "value": {"arguments": [{"kind": "number", "nativeSrc": "4138:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4138:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nativeSrc": "4132:5:1", "nodeType": "YulIdentifier", "src": "4132:5:1"}, "nativeSrc": "4132:9:1", "nodeType": "YulFunctionCall", "src": "4132:9:1"}, "variableNames": [{"name": "memPtr", "nativeSrc": "4122:6:1", "nodeType": "YulIdentifier", "src": "4122:6:1"}]}]}, "name": "allocate_unbounded", "nativeSrc": "4072:75:1", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nativeSrc": "4105:6:1", "nodeType": "YulTypedName", "src": "4105:6:1", "type": ""}], "src": "4072:75:1"}, {"body": {"nativeSrc": "4242:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4242:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "4259:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4259:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "4262:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4262:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "4252:6:1", "nodeType": "YulIdentifier", "src": "4252:6:1"}, "nativeSrc": "4252:12:1", "nodeType": "YulFunctionCall", "src": "4252:12:1"}, "nativeSrc": "4252:12:1", "nodeType": "YulExpressionStatement", "src": "4252:12:1"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "4153:117:1", "nodeType": "YulFunctionDefinition", "src": "4153:117:1"}, {"body": {"nativeSrc": "4365:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4365:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "4382:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4382:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "4385:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4385:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "4375:6:1", "nodeType": "YulIdentifier", "src": "4375:6:1"}, "nativeSrc": "4375:12:1", "nodeType": "YulFunctionCall", "src": "4375:12:1"}, "nativeSrc": "4375:12:1", "nodeType": "YulExpressionStatement", "src": "4375:12:1"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nativeSrc": "4276:117:1", "nodeType": "YulFunctionDefinition", "src": "4276:117:1"}, {"body": {"nativeSrc": "4441:78:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4441:78:1", "statements": [{"body": {"nativeSrc": "4497:16:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4497:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "4506:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4506:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "4509:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4509:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "4499:6:1", "nodeType": "YulIdentifier", "src": "4499:6:1"}, "nativeSrc": "4499:12:1", "nodeType": "YulFunctionCall", "src": "4499:12:1"}, "nativeSrc": "4499:12:1", "nodeType": "YulExpressionStatement", "src": "4499:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "4464:5:1", "nodeType": "YulIdentifier", "src": "4464:5:1"}, {"arguments": [{"name": "value", "nativeSrc": "4488:5:1", "nodeType": "YulIdentifier", "src": "4488:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "4471:16:1", "nodeType": "YulIdentifier", "src": "4471:16:1"}, "nativeSrc": "4471:23:1", "nodeType": "YulFunctionCall", "src": "4471:23:1"}], "functionName": {"name": "eq", "nativeSrc": "4461:2:1", "nodeType": "YulIdentifier", "src": "4461:2:1"}, "nativeSrc": "4461:34:1", "nodeType": "YulFunctionCall", "src": "4461:34:1"}], "functionName": {"name": "iszero", "nativeSrc": "4454:6:1", "nodeType": "YulIdentifier", "src": "4454:6:1"}, "nativeSrc": "4454:42:1", "nodeType": "YulFunctionCall", "src": "4454:42:1"}, "nativeSrc": "4451:62:1", "nodeType": "YulIf", "src": "4451:62:1"}]}, "name": "validator_revert_t_uint32", "nativeSrc": "4399:120:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "4434:5:1", "nodeType": "YulTypedName", "src": "4434:5:1", "type": ""}], "src": "4399:120:1"}, {"body": {"nativeSrc": "4576:86:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4576:86:1", "statements": [{"nativeSrc": "4586:29:1", "nodeType": "YulAssignment", "src": "4586:29:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "4608:6:1", "nodeType": "YulIdentifier", "src": "4608:6:1"}], "functionName": {"name": "calldataload", "nativeSrc": "4595:12:1", "nodeType": "YulIdentifier", "src": "4595:12:1"}, "nativeSrc": "4595:20:1", "nodeType": "YulFunctionCall", "src": "4595:20:1"}, "variableNames": [{"name": "value", "nativeSrc": "4586:5:1", "nodeType": "YulIdentifier", "src": "4586:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "4650:5:1", "nodeType": "YulIdentifier", "src": "4650:5:1"}], "functionName": {"name": "validator_revert_t_uint32", "nativeSrc": "4624:25:1", "nodeType": "YulIdentifier", "src": "4624:25:1"}, "nativeSrc": "4624:32:1", "nodeType": "YulFunctionCall", "src": "4624:32:1"}, "nativeSrc": "4624:32:1", "nodeType": "YulExpressionStatement", "src": "4624:32:1"}]}, "name": "abi_decode_t_uint32", "nativeSrc": "4525:137:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "4554:6:1", "nodeType": "YulTypedName", "src": "4554:6:1", "type": ""}, {"name": "end", "nativeSrc": "4562:3:1", "nodeType": "YulTypedName", "src": "4562:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "4570:5:1", "nodeType": "YulTypedName", "src": "4570:5:1", "type": ""}], "src": "4525:137:1"}, {"body": {"nativeSrc": "4733:262:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4733:262:1", "statements": [{"body": {"nativeSrc": "4779:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4779:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "4781:77:1", "nodeType": "YulIdentifier", "src": "4781:77:1"}, "nativeSrc": "4781:79:1", "nodeType": "YulFunctionCall", "src": "4781:79:1"}, "nativeSrc": "4781:79:1", "nodeType": "YulExpressionStatement", "src": "4781:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "4754:7:1", "nodeType": "YulIdentifier", "src": "4754:7:1"}, {"name": "headStart", "nativeSrc": "4763:9:1", "nodeType": "YulIdentifier", "src": "4763:9:1"}], "functionName": {"name": "sub", "nativeSrc": "4750:3:1", "nodeType": "YulIdentifier", "src": "4750:3:1"}, "nativeSrc": "4750:23:1", "nodeType": "YulFunctionCall", "src": "4750:23:1"}, {"kind": "number", "nativeSrc": "4775:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4775:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "4746:3:1", "nodeType": "YulIdentifier", "src": "4746:3:1"}, "nativeSrc": "4746:32:1", "nodeType": "YulFunctionCall", "src": "4746:32:1"}, "nativeSrc": "4743:119:1", "nodeType": "YulIf", "src": "4743:119:1"}, {"nativeSrc": "4872:116:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4872:116:1", "statements": [{"nativeSrc": "4887:15:1", "nodeType": "YulVariableDeclaration", "src": "4887:15:1", "value": {"kind": "number", "nativeSrc": "4901:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4901:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "4891:6:1", "nodeType": "YulTypedName", "src": "4891:6:1", "type": ""}]}, {"nativeSrc": "4916:62:1", "nodeType": "YulAssignment", "src": "4916:62:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "4950:9:1", "nodeType": "YulIdentifier", "src": "4950:9:1"}, {"name": "offset", "nativeSrc": "4961:6:1", "nodeType": "YulIdentifier", "src": "4961:6:1"}], "functionName": {"name": "add", "nativeSrc": "4946:3:1", "nodeType": "YulIdentifier", "src": "4946:3:1"}, "nativeSrc": "4946:22:1", "nodeType": "YulFunctionCall", "src": "4946:22:1"}, {"name": "dataEnd", "nativeSrc": "4970:7:1", "nodeType": "YulIdentifier", "src": "4970:7:1"}], "functionName": {"name": "abi_decode_t_uint32", "nativeSrc": "4926:19:1", "nodeType": "YulIdentifier", "src": "4926:19:1"}, "nativeSrc": "4926:52:1", "nodeType": "YulFunctionCall", "src": "4926:52:1"}, "variableNames": [{"name": "value0", "nativeSrc": "4916:6:1", "nodeType": "YulIdentifier", "src": "4916:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint32", "nativeSrc": "4668:327:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "4703:9:1", "nodeType": "YulTypedName", "src": "4703:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "4714:7:1", "nodeType": "YulTypedName", "src": "4714:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "4726:6:1", "nodeType": "YulTypedName", "src": "4726:6:1", "type": ""}], "src": "4668:327:1"}, {"body": {"nativeSrc": "5066:53:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5066:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "5083:3:1", "nodeType": "YulIdentifier", "src": "5083:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "5106:5:1", "nodeType": "YulIdentifier", "src": "5106:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "5088:17:1", "nodeType": "YulIdentifier", "src": "5088:17:1"}, "nativeSrc": "5088:24:1", "nodeType": "YulFunctionCall", "src": "5088:24:1"}], "functionName": {"name": "mstore", "nativeSrc": "5076:6:1", "nodeType": "YulIdentifier", "src": "5076:6:1"}, "nativeSrc": "5076:37:1", "nodeType": "YulFunctionCall", "src": "5076:37:1"}, "nativeSrc": "5076:37:1", "nodeType": "YulExpressionStatement", "src": "5076:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "5001:118:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "5054:5:1", "nodeType": "YulTypedName", "src": "5054:5:1", "type": ""}, {"name": "pos", "nativeSrc": "5061:3:1", "nodeType": "YulTypedName", "src": "5061:3:1", "type": ""}], "src": "5001:118:1"}, {"body": {"nativeSrc": "5223:124:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5223:124:1", "statements": [{"nativeSrc": "5233:26:1", "nodeType": "YulAssignment", "src": "5233:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "5245:9:1", "nodeType": "YulIdentifier", "src": "5245:9:1"}, {"kind": "number", "nativeSrc": "5256:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5256:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "5241:3:1", "nodeType": "YulIdentifier", "src": "5241:3:1"}, "nativeSrc": "5241:18:1", "nodeType": "YulFunctionCall", "src": "5241:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "5233:4:1", "nodeType": "YulIdentifier", "src": "5233:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "5313:6:1", "nodeType": "YulIdentifier", "src": "5313:6:1"}, {"arguments": [{"name": "headStart", "nativeSrc": "5326:9:1", "nodeType": "YulIdentifier", "src": "5326:9:1"}, {"kind": "number", "nativeSrc": "5337:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5337:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "5322:3:1", "nodeType": "YulIdentifier", "src": "5322:3:1"}, "nativeSrc": "5322:17:1", "nodeType": "YulFunctionCall", "src": "5322:17:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256_fromStack", "nativeSrc": "5269:43:1", "nodeType": "YulIdentifier", "src": "5269:43:1"}, "nativeSrc": "5269:71:1", "nodeType": "YulFunctionCall", "src": "5269:71:1"}, "nativeSrc": "5269:71:1", "nodeType": "YulExpressionStatement", "src": "5269:71:1"}]}, "name": "abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed", "nativeSrc": "5125:222:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "5195:9:1", "nodeType": "YulTypedName", "src": "5195:9:1", "type": ""}, {"name": "value0", "nativeSrc": "5207:6:1", "nodeType": "YulTypedName", "src": "5207:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "5218:4:1", "nodeType": "YulTypedName", "src": "5218:4:1", "type": ""}], "src": "5125:222:1"}, {"body": {"nativeSrc": "5381:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5381:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "5398:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5398:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "5401:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5401:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "5391:6:1", "nodeType": "YulIdentifier", "src": "5391:6:1"}, "nativeSrc": "5391:88:1", "nodeType": "YulFunctionCall", "src": "5391:88:1"}, "nativeSrc": "5391:88:1", "nodeType": "YulExpressionStatement", "src": "5391:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "5495:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5495:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "5498:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5498:4:1", "type": "", "value": "0x41"}], "functionName": {"name": "mstore", "nativeSrc": "5488:6:1", "nodeType": "YulIdentifier", "src": "5488:6:1"}, "nativeSrc": "5488:15:1", "nodeType": "YulFunctionCall", "src": "5488:15:1"}, "nativeSrc": "5488:15:1", "nodeType": "YulExpressionStatement", "src": "5488:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "5519:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5519:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "5522:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5522:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "5512:6:1", "nodeType": "YulIdentifier", "src": "5512:6:1"}, "nativeSrc": "5512:15:1", "nodeType": "YulFunctionCall", "src": "5512:15:1"}, "nativeSrc": "5512:15:1", "nodeType": "YulExpressionStatement", "src": "5512:15:1"}]}, "name": "panic_error_0x41", "nativeSrc": "5353:180:1", "nodeType": "YulFunctionDefinition", "src": "5353:180:1"}, {"body": {"nativeSrc": "5602:52:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5602:52:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "5619:3:1", "nodeType": "YulIdentifier", "src": "5619:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "5641:5:1", "nodeType": "YulIdentifier", "src": "5641:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "5624:16:1", "nodeType": "YulIdentifier", "src": "5624:16:1"}, "nativeSrc": "5624:23:1", "nodeType": "YulFunctionCall", "src": "5624:23:1"}], "functionName": {"name": "mstore", "nativeSrc": "5612:6:1", "nodeType": "YulIdentifier", "src": "5612:6:1"}, "nativeSrc": "5612:36:1", "nodeType": "YulFunctionCall", "src": "5612:36:1"}, "nativeSrc": "5612:36:1", "nodeType": "YulExpressionStatement", "src": "5612:36:1"}]}, "name": "abi_encode_t_uint32_to_t_uint32_fromStack", "nativeSrc": "5539:115:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "5590:5:1", "nodeType": "YulTypedName", "src": "5590:5:1", "type": ""}, {"name": "pos", "nativeSrc": "5597:3:1", "nodeType": "YulTypedName", "src": "5597:3:1", "type": ""}], "src": "5539:115:1"}, {"body": {"nativeSrc": "5756:122:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5756:122:1", "statements": [{"nativeSrc": "5766:26:1", "nodeType": "YulAssignment", "src": "5766:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "5778:9:1", "nodeType": "YulIdentifier", "src": "5778:9:1"}, {"kind": "number", "nativeSrc": "5789:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5789:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "5774:3:1", "nodeType": "YulIdentifier", "src": "5774:3:1"}, "nativeSrc": "5774:18:1", "nodeType": "YulFunctionCall", "src": "5774:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "5766:4:1", "nodeType": "YulIdentifier", "src": "5766:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "5844:6:1", "nodeType": "YulIdentifier", "src": "5844:6:1"}, {"arguments": [{"name": "headStart", "nativeSrc": "5857:9:1", "nodeType": "YulIdentifier", "src": "5857:9:1"}, {"kind": "number", "nativeSrc": "5868:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5868:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "5853:3:1", "nodeType": "YulIdentifier", "src": "5853:3:1"}, "nativeSrc": "5853:17:1", "nodeType": "YulFunctionCall", "src": "5853:17:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32_fromStack", "nativeSrc": "5802:41:1", "nodeType": "YulIdentifier", "src": "5802:41:1"}, "nativeSrc": "5802:69:1", "nodeType": "YulFunctionCall", "src": "5802:69:1"}, "nativeSrc": "5802:69:1", "nodeType": "YulExpressionStatement", "src": "5802:69:1"}]}, "name": "abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed", "nativeSrc": "5660:218:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "5728:9:1", "nodeType": "YulTypedName", "src": "5728:9:1", "type": ""}, {"name": "value0", "nativeSrc": "5740:6:1", "nodeType": "YulTypedName", "src": "5740:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "5751:4:1", "nodeType": "YulTypedName", "src": "5751:4:1", "type": ""}], "src": "5660:218:1"}, {"body": {"nativeSrc": "5942:40:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5942:40:1", "statements": [{"nativeSrc": "5953:22:1", "nodeType": "YulAssignment", "src": "5953:22:1", "value": {"arguments": [{"name": "value", "nativeSrc": "5969:5:1", "nodeType": "YulIdentifier", "src": "5969:5:1"}], "functionName": {"name": "mload", "nativeSrc": "5963:5:1", "nodeType": "YulIdentifier", "src": "5963:5:1"}, "nativeSrc": "5963:12:1", "nodeType": "YulFunctionCall", "src": "5963:12:1"}, "variableNames": [{"name": "length", "nativeSrc": "5953:6:1", "nodeType": "YulIdentifier", "src": "5953:6:1"}]}]}, "name": "array_length_t_bytes_memory_ptr", "nativeSrc": "5884:98:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "5925:5:1", "nodeType": "YulTypedName", "src": "5925:5:1", "type": ""}], "returnVariables": [{"name": "length", "nativeSrc": "5935:6:1", "nodeType": "YulTypedName", "src": "5935:6:1", "type": ""}], "src": "5884:98:1"}, {"body": {"nativeSrc": "6101:34:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6101:34:1", "statements": [{"nativeSrc": "6111:18:1", "nodeType": "YulAssignment", "src": "6111:18:1", "value": {"name": "pos", "nativeSrc": "6126:3:1", "nodeType": "YulIdentifier", "src": "6126:3:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "6111:11:1", "nodeType": "YulIdentifier", "src": "6111:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nativeSrc": "5988:147:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "6073:3:1", "nodeType": "YulTypedName", "src": "6073:3:1", "type": ""}, {"name": "length", "nativeSrc": "6078:6:1", "nodeType": "YulTypedName", "src": "6078:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "6089:11:1", "nodeType": "YulTypedName", "src": "6089:11:1", "type": ""}], "src": "5988:147:1"}, {"body": {"nativeSrc": "6203:186:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6203:186:1", "statements": [{"nativeSrc": "6214:10:1", "nodeType": "YulVariableDeclaration", "src": "6214:10:1", "value": {"kind": "number", "nativeSrc": "6223:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6223:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nativeSrc": "6218:1:1", "nodeType": "YulTypedName", "src": "6218:1:1", "type": ""}]}, {"body": {"nativeSrc": "6283:63:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6283:63:1", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "dst", "nativeSrc": "6308:3:1", "nodeType": "YulIdentifier", "src": "6308:3:1"}, {"name": "i", "nativeSrc": "6313:1:1", "nodeType": "YulIdentifier", "src": "6313:1:1"}], "functionName": {"name": "add", "nativeSrc": "6304:3:1", "nodeType": "YulIdentifier", "src": "6304:3:1"}, "nativeSrc": "6304:11:1", "nodeType": "YulFunctionCall", "src": "6304:11:1"}, {"arguments": [{"arguments": [{"name": "src", "nativeSrc": "6327:3:1", "nodeType": "YulIdentifier", "src": "6327:3:1"}, {"name": "i", "nativeSrc": "6332:1:1", "nodeType": "YulIdentifier", "src": "6332:1:1"}], "functionName": {"name": "add", "nativeSrc": "6323:3:1", "nodeType": "YulIdentifier", "src": "6323:3:1"}, "nativeSrc": "6323:11:1", "nodeType": "YulFunctionCall", "src": "6323:11:1"}], "functionName": {"name": "mload", "nativeSrc": "6317:5:1", "nodeType": "YulIdentifier", "src": "6317:5:1"}, "nativeSrc": "6317:18:1", "nodeType": "YulFunctionCall", "src": "6317:18:1"}], "functionName": {"name": "mstore", "nativeSrc": "6297:6:1", "nodeType": "YulIdentifier", "src": "6297:6:1"}, "nativeSrc": "6297:39:1", "nodeType": "YulFunctionCall", "src": "6297:39:1"}, "nativeSrc": "6297:39:1", "nodeType": "YulExpressionStatement", "src": "6297:39:1"}]}, "condition": {"arguments": [{"name": "i", "nativeSrc": "6244:1:1", "nodeType": "YulIdentifier", "src": "6244:1:1"}, {"name": "length", "nativeSrc": "6247:6:1", "nodeType": "YulIdentifier", "src": "6247:6:1"}], "functionName": {"name": "lt", "nativeSrc": "6241:2:1", "nodeType": "YulIdentifier", "src": "6241:2:1"}, "nativeSrc": "6241:13:1", "nodeType": "YulFunctionCall", "src": "6241:13:1"}, "nativeSrc": "6233:113:1", "nodeType": "YulForLoop", "post": {"nativeSrc": "6255:19:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6255:19:1", "statements": [{"nativeSrc": "6257:15:1", "nodeType": "YulAssignment", "src": "6257:15:1", "value": {"arguments": [{"name": "i", "nativeSrc": "6266:1:1", "nodeType": "YulIdentifier", "src": "6266:1:1"}, {"kind": "number", "nativeSrc": "6269:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6269:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "6262:3:1", "nodeType": "YulIdentifier", "src": "6262:3:1"}, "nativeSrc": "6262:10:1", "nodeType": "YulFunctionCall", "src": "6262:10:1"}, "variableNames": [{"name": "i", "nativeSrc": "6257:1:1", "nodeType": "YulIdentifier", "src": "6257:1:1"}]}]}, "pre": {"nativeSrc": "6237:3:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6237:3:1", "statements": []}, "src": "6233:113:1"}, {"expression": {"arguments": [{"arguments": [{"name": "dst", "nativeSrc": "6366:3:1", "nodeType": "YulIdentifier", "src": "6366:3:1"}, {"name": "length", "nativeSrc": "6371:6:1", "nodeType": "YulIdentifier", "src": "6371:6:1"}], "functionName": {"name": "add", "nativeSrc": "6362:3:1", "nodeType": "YulIdentifier", "src": "6362:3:1"}, "nativeSrc": "6362:16:1", "nodeType": "YulFunctionCall", "src": "6362:16:1"}, {"kind": "number", "nativeSrc": "6380:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6380:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nativeSrc": "6355:6:1", "nodeType": "YulIdentifier", "src": "6355:6:1"}, "nativeSrc": "6355:27:1", "nodeType": "YulFunctionCall", "src": "6355:27:1"}, "nativeSrc": "6355:27:1", "nodeType": "YulExpressionStatement", "src": "6355:27:1"}]}, "name": "copy_memory_to_memory_with_cleanup", "nativeSrc": "6141:248:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nativeSrc": "6185:3:1", "nodeType": "YulTypedName", "src": "6185:3:1", "type": ""}, {"name": "dst", "nativeSrc": "6190:3:1", "nodeType": "YulTypedName", "src": "6190:3:1", "type": ""}, {"name": "length", "nativeSrc": "6195:6:1", "nodeType": "YulTypedName", "src": "6195:6:1", "type": ""}], "src": "6141:248:1"}, {"body": {"nativeSrc": "6503:278:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6503:278:1", "statements": [{"nativeSrc": "6513:52:1", "nodeType": "YulVariableDeclaration", "src": "6513:52:1", "value": {"arguments": [{"name": "value", "nativeSrc": "6559:5:1", "nodeType": "YulIdentifier", "src": "6559:5:1"}], "functionName": {"name": "array_length_t_bytes_memory_ptr", "nativeSrc": "6527:31:1", "nodeType": "YulIdentifier", "src": "6527:31:1"}, "nativeSrc": "6527:38:1", "nodeType": "YulFunctionCall", "src": "6527:38:1"}, "variables": [{"name": "length", "nativeSrc": "6517:6:1", "nodeType": "YulTypedName", "src": "6517:6:1", "type": ""}]}, {"nativeSrc": "6574:95:1", "nodeType": "YulAssignment", "src": "6574:95:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "6657:3:1", "nodeType": "YulIdentifier", "src": "6657:3:1"}, {"name": "length", "nativeSrc": "6662:6:1", "nodeType": "YulIdentifier", "src": "6662:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nativeSrc": "6581:75:1", "nodeType": "YulIdentifier", "src": "6581:75:1"}, "nativeSrc": "6581:88:1", "nodeType": "YulFunctionCall", "src": "6581:88:1"}, "variableNames": [{"name": "pos", "nativeSrc": "6574:3:1", "nodeType": "YulIdentifier", "src": "6574:3:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "6717:5:1", "nodeType": "YulIdentifier", "src": "6717:5:1"}, {"kind": "number", "nativeSrc": "6724:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6724:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "6713:3:1", "nodeType": "YulIdentifier", "src": "6713:3:1"}, "nativeSrc": "6713:16:1", "nodeType": "YulFunctionCall", "src": "6713:16:1"}, {"name": "pos", "nativeSrc": "6731:3:1", "nodeType": "YulIdentifier", "src": "6731:3:1"}, {"name": "length", "nativeSrc": "6736:6:1", "nodeType": "YulIdentifier", "src": "6736:6:1"}], "functionName": {"name": "copy_memory_to_memory_with_cleanup", "nativeSrc": "6678:34:1", "nodeType": "YulIdentifier", "src": "6678:34:1"}, "nativeSrc": "6678:65:1", "nodeType": "YulFunctionCall", "src": "6678:65:1"}, "nativeSrc": "6678:65:1", "nodeType": "YulExpressionStatement", "src": "6678:65:1"}, {"nativeSrc": "6752:23:1", "nodeType": "YulAssignment", "src": "6752:23:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "6763:3:1", "nodeType": "YulIdentifier", "src": "6763:3:1"}, {"name": "length", "nativeSrc": "6768:6:1", "nodeType": "YulIdentifier", "src": "6768:6:1"}], "functionName": {"name": "add", "nativeSrc": "6759:3:1", "nodeType": "YulIdentifier", "src": "6759:3:1"}, "nativeSrc": "6759:16:1", "nodeType": "YulFunctionCall", "src": "6759:16:1"}, "variableNames": [{"name": "end", "nativeSrc": "6752:3:1", "nodeType": "YulIdentifier", "src": "6752:3:1"}]}]}, "name": "abi_encode_t_bytes_memory_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nativeSrc": "6395:386:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "6484:5:1", "nodeType": "YulTypedName", "src": "6484:5:1", "type": ""}, {"name": "pos", "nativeSrc": "6491:3:1", "nodeType": "YulTypedName", "src": "6491:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "6499:3:1", "nodeType": "YulTypedName", "src": "6499:3:1", "type": ""}], "src": "6395:386:1"}, {"body": {"nativeSrc": "6921:137:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6921:137:1", "statements": [{"nativeSrc": "6932:100:1", "nodeType": "YulAssignment", "src": "6932:100:1", "value": {"arguments": [{"name": "value0", "nativeSrc": "7019:6:1", "nodeType": "YulIdentifier", "src": "7019:6:1"}, {"name": "pos", "nativeSrc": "7028:3:1", "nodeType": "YulIdentifier", "src": "7028:3:1"}], "functionName": {"name": "abi_encode_t_bytes_memory_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack", "nativeSrc": "6939:79:1", "nodeType": "YulIdentifier", "src": "6939:79:1"}, "nativeSrc": "6939:93:1", "nodeType": "YulFunctionCall", "src": "6939:93:1"}, "variableNames": [{"name": "pos", "nativeSrc": "6932:3:1", "nodeType": "YulIdentifier", "src": "6932:3:1"}]}, {"nativeSrc": "7042:10:1", "nodeType": "YulAssignment", "src": "7042:10:1", "value": {"name": "pos", "nativeSrc": "7049:3:1", "nodeType": "YulIdentifier", "src": "7049:3:1"}, "variableNames": [{"name": "end", "nativeSrc": "7042:3:1", "nodeType": "YulIdentifier", "src": "7042:3:1"}]}]}, "name": "abi_encode_tuple_packed_t_bytes_memory_ptr__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed", "nativeSrc": "6787:271:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "6900:3:1", "nodeType": "YulTypedName", "src": "6900:3:1", "type": ""}, {"name": "value0", "nativeSrc": "6906:6:1", "nodeType": "YulTypedName", "src": "6906:6:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "6917:3:1", "nodeType": "YulTypedName", "src": "6917:3:1", "type": ""}], "src": "6787:271:1"}, {"body": {"nativeSrc": "7108:57:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7108:57:1", "statements": [{"nativeSrc": "7118:41:1", "nodeType": "YulAssignment", "src": "7118:41:1", "value": {"arguments": [{"name": "value", "nativeSrc": "7133:5:1", "nodeType": "YulIdentifier", "src": "7133:5:1"}, {"kind": "number", "nativeSrc": "7140:18:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7140:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "and", "nativeSrc": "7129:3:1", "nodeType": "YulIdentifier", "src": "7129:3:1"}, "nativeSrc": "7129:30:1", "nodeType": "YulFunctionCall", "src": "7129:30:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "7118:7:1", "nodeType": "YulIdentifier", "src": "7118:7:1"}]}]}, "name": "cleanup_t_uint64", "nativeSrc": "7064:101:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "7090:5:1", "nodeType": "YulTypedName", "src": "7090:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "7100:7:1", "nodeType": "YulTypedName", "src": "7100:7:1", "type": ""}], "src": "7064:101:1"}, {"body": {"nativeSrc": "7213:78:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7213:78:1", "statements": [{"body": {"nativeSrc": "7269:16:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7269:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "7278:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7278:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "7281:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7281:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "7271:6:1", "nodeType": "YulIdentifier", "src": "7271:6:1"}, "nativeSrc": "7271:12:1", "nodeType": "YulFunctionCall", "src": "7271:12:1"}, "nativeSrc": "7271:12:1", "nodeType": "YulExpressionStatement", "src": "7271:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "7236:5:1", "nodeType": "YulIdentifier", "src": "7236:5:1"}, {"arguments": [{"name": "value", "nativeSrc": "7260:5:1", "nodeType": "YulIdentifier", "src": "7260:5:1"}], "functionName": {"name": "cleanup_t_uint64", "nativeSrc": "7243:16:1", "nodeType": "YulIdentifier", "src": "7243:16:1"}, "nativeSrc": "7243:23:1", "nodeType": "YulFunctionCall", "src": "7243:23:1"}], "functionName": {"name": "eq", "nativeSrc": "7233:2:1", "nodeType": "YulIdentifier", "src": "7233:2:1"}, "nativeSrc": "7233:34:1", "nodeType": "YulFunctionCall", "src": "7233:34:1"}], "functionName": {"name": "iszero", "nativeSrc": "7226:6:1", "nodeType": "YulIdentifier", "src": "7226:6:1"}, "nativeSrc": "7226:42:1", "nodeType": "YulFunctionCall", "src": "7226:42:1"}, "nativeSrc": "7223:62:1", "nodeType": "YulIf", "src": "7223:62:1"}]}, "name": "validator_revert_t_uint64", "nativeSrc": "7171:120:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "7206:5:1", "nodeType": "YulTypedName", "src": "7206:5:1", "type": ""}], "src": "7171:120:1"}, {"body": {"nativeSrc": "7359:79:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7359:79:1", "statements": [{"nativeSrc": "7369:22:1", "nodeType": "YulAssignment", "src": "7369:22:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "7384:6:1", "nodeType": "YulIdentifier", "src": "7384:6:1"}], "functionName": {"name": "mload", "nativeSrc": "7378:5:1", "nodeType": "YulIdentifier", "src": "7378:5:1"}, "nativeSrc": "7378:13:1", "nodeType": "YulFunctionCall", "src": "7378:13:1"}, "variableNames": [{"name": "value", "nativeSrc": "7369:5:1", "nodeType": "YulIdentifier", "src": "7369:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "7426:5:1", "nodeType": "YulIdentifier", "src": "7426:5:1"}], "functionName": {"name": "validator_revert_t_uint64", "nativeSrc": "7400:25:1", "nodeType": "YulIdentifier", "src": "7400:25:1"}, "nativeSrc": "7400:32:1", "nodeType": "YulFunctionCall", "src": "7400:32:1"}, "nativeSrc": "7400:32:1", "nodeType": "YulExpressionStatement", "src": "7400:32:1"}]}, "name": "abi_decode_t_uint64_fromMemory", "nativeSrc": "7297:141:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "7337:6:1", "nodeType": "YulTypedName", "src": "7337:6:1", "type": ""}, {"name": "end", "nativeSrc": "7345:3:1", "nodeType": "YulTypedName", "src": "7345:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "7353:5:1", "nodeType": "YulTypedName", "src": "7353:5:1", "type": ""}], "src": "7297:141:1"}, {"body": {"nativeSrc": "7520:273:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7520:273:1", "statements": [{"body": {"nativeSrc": "7566:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7566:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "7568:77:1", "nodeType": "YulIdentifier", "src": "7568:77:1"}, "nativeSrc": "7568:79:1", "nodeType": "YulFunctionCall", "src": "7568:79:1"}, "nativeSrc": "7568:79:1", "nodeType": "YulExpressionStatement", "src": "7568:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "7541:7:1", "nodeType": "YulIdentifier", "src": "7541:7:1"}, {"name": "headStart", "nativeSrc": "7550:9:1", "nodeType": "YulIdentifier", "src": "7550:9:1"}], "functionName": {"name": "sub", "nativeSrc": "7537:3:1", "nodeType": "YulIdentifier", "src": "7537:3:1"}, "nativeSrc": "7537:23:1", "nodeType": "YulFunctionCall", "src": "7537:23:1"}, {"kind": "number", "nativeSrc": "7562:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7562:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "7533:3:1", "nodeType": "YulIdentifier", "src": "7533:3:1"}, "nativeSrc": "7533:32:1", "nodeType": "YulFunctionCall", "src": "7533:32:1"}, "nativeSrc": "7530:119:1", "nodeType": "YulIf", "src": "7530:119:1"}, {"nativeSrc": "7659:127:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7659:127:1", "statements": [{"nativeSrc": "7674:15:1", "nodeType": "YulVariableDeclaration", "src": "7674:15:1", "value": {"kind": "number", "nativeSrc": "7688:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7688:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "7678:6:1", "nodeType": "YulTypedName", "src": "7678:6:1", "type": ""}]}, {"nativeSrc": "7703:73:1", "nodeType": "YulAssignment", "src": "7703:73:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "7748:9:1", "nodeType": "YulIdentifier", "src": "7748:9:1"}, {"name": "offset", "nativeSrc": "7759:6:1", "nodeType": "YulIdentifier", "src": "7759:6:1"}], "functionName": {"name": "add", "nativeSrc": "7744:3:1", "nodeType": "YulIdentifier", "src": "7744:3:1"}, "nativeSrc": "7744:22:1", "nodeType": "YulFunctionCall", "src": "7744:22:1"}, {"name": "dataEnd", "nativeSrc": "7768:7:1", "nodeType": "YulIdentifier", "src": "7768:7:1"}], "functionName": {"name": "abi_decode_t_uint64_fromMemory", "nativeSrc": "7713:30:1", "nodeType": "YulIdentifier", "src": "7713:30:1"}, "nativeSrc": "7713:63:1", "nodeType": "YulFunctionCall", "src": "7713:63:1"}, "variableNames": [{"name": "value0", "nativeSrc": "7703:6:1", "nodeType": "YulIdentifier", "src": "7703:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint64_fromMemory", "nativeSrc": "7444:349:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "7490:9:1", "nodeType": "YulTypedName", "src": "7490:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "7501:7:1", "nodeType": "YulTypedName", "src": "7501:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "7513:6:1", "nodeType": "YulTypedName", "src": "7513:6:1", "type": ""}], "src": "7444:349:1"}, {"body": {"nativeSrc": "7827:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7827:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "7844:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7844:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "7847:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7847:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "7837:6:1", "nodeType": "YulIdentifier", "src": "7837:6:1"}, "nativeSrc": "7837:88:1", "nodeType": "YulFunctionCall", "src": "7837:88:1"}, "nativeSrc": "7837:88:1", "nodeType": "YulExpressionStatement", "src": "7837:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "7941:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7941:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "7944:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7944:4:1", "type": "", "value": "0x32"}], "functionName": {"name": "mstore", "nativeSrc": "7934:6:1", "nodeType": "YulIdentifier", "src": "7934:6:1"}, "nativeSrc": "7934:15:1", "nodeType": "YulFunctionCall", "src": "7934:15:1"}, "nativeSrc": "7934:15:1", "nodeType": "YulExpressionStatement", "src": "7934:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "7965:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7965:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "7968:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7968:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "7958:6:1", "nodeType": "YulIdentifier", "src": "7958:6:1"}, "nativeSrc": "7958:15:1", "nodeType": "YulFunctionCall", "src": "7958:15:1"}, "nativeSrc": "7958:15:1", "nodeType": "YulExpressionStatement", "src": "7958:15:1"}]}, "name": "panic_error_0x32", "nativeSrc": "7799:180:1", "nodeType": "YulFunctionDefinition", "src": "7799:180:1"}, {"body": {"nativeSrc": "8013:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8013:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "8030:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8030:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "8033:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8033:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "8023:6:1", "nodeType": "YulIdentifier", "src": "8023:6:1"}, "nativeSrc": "8023:88:1", "nodeType": "YulFunctionCall", "src": "8023:88:1"}, "nativeSrc": "8023:88:1", "nodeType": "YulExpressionStatement", "src": "8023:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "8127:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8127:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "8130:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8130:4:1", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nativeSrc": "8120:6:1", "nodeType": "YulIdentifier", "src": "8120:6:1"}, "nativeSrc": "8120:15:1", "nodeType": "YulFunctionCall", "src": "8120:15:1"}, "nativeSrc": "8120:15:1", "nodeType": "YulExpressionStatement", "src": "8120:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "8151:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8151:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "8154:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8154:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "8144:6:1", "nodeType": "YulIdentifier", "src": "8144:6:1"}, "nativeSrc": "8144:15:1", "nodeType": "YulFunctionCall", "src": "8144:15:1"}, "nativeSrc": "8144:15:1", "nodeType": "YulExpressionStatement", "src": "8144:15:1"}]}, "name": "panic_error_0x11", "nativeSrc": "7985:180:1", "nodeType": "YulFunctionDefinition", "src": "7985:180:1"}, {"body": {"nativeSrc": "8214:190:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8214:190:1", "statements": [{"nativeSrc": "8224:33:1", "nodeType": "YulAssignment", "src": "8224:33:1", "value": {"arguments": [{"name": "value", "nativeSrc": "8251:5:1", "nodeType": "YulIdentifier", "src": "8251:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "8233:17:1", "nodeType": "YulIdentifier", "src": "8233:17:1"}, "nativeSrc": "8233:24:1", "nodeType": "YulFunctionCall", "src": "8233:24:1"}, "variableNames": [{"name": "value", "nativeSrc": "8224:5:1", "nodeType": "YulIdentifier", "src": "8224:5:1"}]}, {"body": {"nativeSrc": "8347:22:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8347:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x11", "nativeSrc": "8349:16:1", "nodeType": "YulIdentifier", "src": "8349:16:1"}, "nativeSrc": "8349:18:1", "nodeType": "YulFunctionCall", "src": "8349:18:1"}, "nativeSrc": "8349:18:1", "nodeType": "YulExpressionStatement", "src": "8349:18:1"}]}, "condition": {"arguments": [{"name": "value", "nativeSrc": "8272:5:1", "nodeType": "YulIdentifier", "src": "8272:5:1"}, {"kind": "number", "nativeSrc": "8279:66:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8279:66:1", "type": "", "value": "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"}], "functionName": {"name": "eq", "nativeSrc": "8269:2:1", "nodeType": "YulIdentifier", "src": "8269:2:1"}, "nativeSrc": "8269:77:1", "nodeType": "YulFunctionCall", "src": "8269:77:1"}, "nativeSrc": "8266:103:1", "nodeType": "YulIf", "src": "8266:103:1"}, {"nativeSrc": "8378:20:1", "nodeType": "YulAssignment", "src": "8378:20:1", "value": {"arguments": [{"name": "value", "nativeSrc": "8389:5:1", "nodeType": "YulIdentifier", "src": "8389:5:1"}, {"kind": "number", "nativeSrc": "8396:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8396:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nativeSrc": "8385:3:1", "nodeType": "YulIdentifier", "src": "8385:3:1"}, "nativeSrc": "8385:13:1", "nodeType": "YulFunctionCall", "src": "8385:13:1"}, "variableNames": [{"name": "ret", "nativeSrc": "8378:3:1", "nodeType": "YulIdentifier", "src": "8378:3:1"}]}]}, "name": "increment_t_uint256", "nativeSrc": "8171:233:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "8200:5:1", "nodeType": "YulTypedName", "src": "8200:5:1", "type": ""}], "returnVariables": [{"name": "ret", "nativeSrc": "8210:3:1", "nodeType": "YulTypedName", "src": "8210:3:1", "type": ""}], "src": "8171:233:1"}, {"body": {"nativeSrc": "8506:73:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8506:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "8523:3:1", "nodeType": "YulIdentifier", "src": "8523:3:1"}, {"name": "length", "nativeSrc": "8528:6:1", "nodeType": "YulIdentifier", "src": "8528:6:1"}], "functionName": {"name": "mstore", "nativeSrc": "8516:6:1", "nodeType": "YulIdentifier", "src": "8516:6:1"}, "nativeSrc": "8516:19:1", "nodeType": "YulFunctionCall", "src": "8516:19:1"}, "nativeSrc": "8516:19:1", "nodeType": "YulExpressionStatement", "src": "8516:19:1"}, {"nativeSrc": "8544:29:1", "nodeType": "YulAssignment", "src": "8544:29:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "8563:3:1", "nodeType": "YulIdentifier", "src": "8563:3:1"}, {"kind": "number", "nativeSrc": "8568:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8568:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "8559:3:1", "nodeType": "YulIdentifier", "src": "8559:3:1"}, "nativeSrc": "8559:14:1", "nodeType": "YulFunctionCall", "src": "8559:14:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "8544:11:1", "nodeType": "YulIdentifier", "src": "8544:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "8410:169:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "8478:3:1", "nodeType": "YulTypedName", "src": "8478:3:1", "type": ""}, {"name": "length", "nativeSrc": "8483:6:1", "nodeType": "YulTypedName", "src": "8483:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "8494:11:1", "nodeType": "YulTypedName", "src": "8494:11:1", "type": ""}], "src": "8410:169:1"}, {"body": {"nativeSrc": "8691:63:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8691:63:1", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "memPtr", "nativeSrc": "8713:6:1", "nodeType": "YulIdentifier", "src": "8713:6:1"}, {"kind": "number", "nativeSrc": "8721:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8721:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "8709:3:1", "nodeType": "YulIdentifier", "src": "8709:3:1"}, "nativeSrc": "8709:14:1", "nodeType": "YulFunctionCall", "src": "8709:14:1"}, {"hexValue": "417373657420646f65736e2774206578697374", "kind": "string", "nativeSrc": "8725:21:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8725:21:1", "type": "", "value": "Asset doesn't exist"}], "functionName": {"name": "mstore", "nativeSrc": "8702:6:1", "nodeType": "YulIdentifier", "src": "8702:6:1"}, "nativeSrc": "8702:45:1", "nodeType": "YulFunctionCall", "src": "8702:45:1"}, "nativeSrc": "8702:45:1", "nodeType": "YulExpressionStatement", "src": "8702:45:1"}]}, "name": "store_literal_in_memory_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc", "nativeSrc": "8585:169:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "memPtr", "nativeSrc": "8683:6:1", "nodeType": "YulTypedName", "src": "8683:6:1", "type": ""}], "src": "8585:169:1"}, {"body": {"nativeSrc": "8906:220:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8906:220:1", "statements": [{"nativeSrc": "8916:74:1", "nodeType": "YulAssignment", "src": "8916:74:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "8982:3:1", "nodeType": "YulIdentifier", "src": "8982:3:1"}, {"kind": "number", "nativeSrc": "8987:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8987:2:1", "type": "", "value": "19"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr_fromStack", "nativeSrc": "8923:58:1", "nodeType": "YulIdentifier", "src": "8923:58:1"}, "nativeSrc": "8923:67:1", "nodeType": "YulFunctionCall", "src": "8923:67:1"}, "variableNames": [{"name": "pos", "nativeSrc": "8916:3:1", "nodeType": "YulIdentifier", "src": "8916:3:1"}]}, {"expression": {"arguments": [{"name": "pos", "nativeSrc": "9088:3:1", "nodeType": "YulIdentifier", "src": "9088:3:1"}], "functionName": {"name": "store_literal_in_memory_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc", "nativeSrc": "8999:88:1", "nodeType": "YulIdentifier", "src": "8999:88:1"}, "nativeSrc": "8999:93:1", "nodeType": "YulFunctionCall", "src": "8999:93:1"}, "nativeSrc": "8999:93:1", "nodeType": "YulExpressionStatement", "src": "8999:93:1"}, {"nativeSrc": "9101:19:1", "nodeType": "YulAssignment", "src": "9101:19:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "9112:3:1", "nodeType": "YulIdentifier", "src": "9112:3:1"}, {"kind": "number", "nativeSrc": "9117:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9117:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "9108:3:1", "nodeType": "YulIdentifier", "src": "9108:3:1"}, "nativeSrc": "9108:12:1", "nodeType": "YulFunctionCall", "src": "9108:12:1"}, "variableNames": [{"name": "end", "nativeSrc": "9101:3:1", "nodeType": "YulIdentifier", "src": "9101:3:1"}]}]}, "name": "abi_encode_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc_to_t_string_memory_ptr_fromStack", "nativeSrc": "8760:366:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "8894:3:1", "nodeType": "YulTypedName", "src": "8894:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "8902:3:1", "nodeType": "YulTypedName", "src": "8902:3:1", "type": ""}], "src": "8760:366:1"}, {"body": {"nativeSrc": "9303:248:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9303:248:1", "statements": [{"nativeSrc": "9313:26:1", "nodeType": "YulAssignment", "src": "9313:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "9325:9:1", "nodeType": "YulIdentifier", "src": "9325:9:1"}, {"kind": "number", "nativeSrc": "9336:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9336:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "9321:3:1", "nodeType": "YulIdentifier", "src": "9321:3:1"}, "nativeSrc": "9321:18:1", "nodeType": "YulFunctionCall", "src": "9321:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "9313:4:1", "nodeType": "YulIdentifier", "src": "9313:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "9360:9:1", "nodeType": "YulIdentifier", "src": "9360:9:1"}, {"kind": "number", "nativeSrc": "9371:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9371:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "9356:3:1", "nodeType": "YulIdentifier", "src": "9356:3:1"}, "nativeSrc": "9356:17:1", "nodeType": "YulFunctionCall", "src": "9356:17:1"}, {"arguments": [{"name": "tail", "nativeSrc": "9379:4:1", "nodeType": "YulIdentifier", "src": "9379:4:1"}, {"name": "headStart", "nativeSrc": "9385:9:1", "nodeType": "YulIdentifier", "src": "9385:9:1"}], "functionName": {"name": "sub", "nativeSrc": "9375:3:1", "nodeType": "YulIdentifier", "src": "9375:3:1"}, "nativeSrc": "9375:20:1", "nodeType": "YulFunctionCall", "src": "9375:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "9349:6:1", "nodeType": "YulIdentifier", "src": "9349:6:1"}, "nativeSrc": "9349:47:1", "nodeType": "YulFunctionCall", "src": "9349:47:1"}, "nativeSrc": "9349:47:1", "nodeType": "YulExpressionStatement", "src": "9349:47:1"}, {"nativeSrc": "9405:139:1", "nodeType": "YulAssignment", "src": "9405:139:1", "value": {"arguments": [{"name": "tail", "nativeSrc": "9539:4:1", "nodeType": "YulIdentifier", "src": "9539:4:1"}], "functionName": {"name": "abi_encode_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc_to_t_string_memory_ptr_fromStack", "nativeSrc": "9413:124:1", "nodeType": "YulIdentifier", "src": "9413:124:1"}, "nativeSrc": "9413:131:1", "nodeType": "YulFunctionCall", "src": "9413:131:1"}, "variableNames": [{"name": "tail", "nativeSrc": "9405:4:1", "nodeType": "YulIdentifier", "src": "9405:4:1"}]}]}, "name": "abi_encode_tuple_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc__to_t_string_memory_ptr__fromStack_reversed", "nativeSrc": "9132:419:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "9283:9:1", "nodeType": "YulTypedName", "src": "9283:9:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "9298:4:1", "nodeType": "YulTypedName", "src": "9298:4:1", "type": ""}], "src": "9132:419:1"}, {"body": {"nativeSrc": "9585:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9585:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "9602:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9602:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9605:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9605:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "9595:6:1", "nodeType": "YulIdentifier", "src": "9595:6:1"}, "nativeSrc": "9595:88:1", "nodeType": "YulFunctionCall", "src": "9595:88:1"}, "nativeSrc": "9595:88:1", "nodeType": "YulExpressionStatement", "src": "9595:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9699:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9699:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "9702:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9702:4:1", "type": "", "value": "0x12"}], "functionName": {"name": "mstore", "nativeSrc": "9692:6:1", "nodeType": "YulIdentifier", "src": "9692:6:1"}, "nativeSrc": "9692:15:1", "nodeType": "YulFunctionCall", "src": "9692:15:1"}, "nativeSrc": "9692:15:1", "nodeType": "YulExpressionStatement", "src": "9692:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9723:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9723:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9726:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9726:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "9716:6:1", "nodeType": "YulIdentifier", "src": "9716:6:1"}, "nativeSrc": "9716:15:1", "nodeType": "YulFunctionCall", "src": "9716:15:1"}, "nativeSrc": "9716:15:1", "nodeType": "YulExpressionStatement", "src": "9716:15:1"}]}, "name": "panic_error_0x12", "nativeSrc": "9557:180:1", "nodeType": "YulFunctionDefinition", "src": "9557:180:1"}, {"body": {"nativeSrc": "9785:143:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9785:143:1", "statements": [{"nativeSrc": "9795:25:1", "nodeType": "YulAssignment", "src": "9795:25:1", "value": {"arguments": [{"name": "x", "nativeSrc": "9818:1:1", "nodeType": "YulIdentifier", "src": "9818:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "9800:17:1", "nodeType": "YulIdentifier", "src": "9800:17:1"}, "nativeSrc": "9800:20:1", "nodeType": "YulFunctionCall", "src": "9800:20:1"}, "variableNames": [{"name": "x", "nativeSrc": "9795:1:1", "nodeType": "YulIdentifier", "src": "9795:1:1"}]}, {"nativeSrc": "9829:25:1", "nodeType": "YulAssignment", "src": "9829:25:1", "value": {"arguments": [{"name": "y", "nativeSrc": "9852:1:1", "nodeType": "YulIdentifier", "src": "9852:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "9834:17:1", "nodeType": "YulIdentifier", "src": "9834:17:1"}, "nativeSrc": "9834:20:1", "nodeType": "YulFunctionCall", "src": "9834:20:1"}, "variableNames": [{"name": "y", "nativeSrc": "9829:1:1", "nodeType": "YulIdentifier", "src": "9829:1:1"}]}, {"body": {"nativeSrc": "9876:22:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9876:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x12", "nativeSrc": "9878:16:1", "nodeType": "YulIdentifier", "src": "9878:16:1"}, "nativeSrc": "9878:18:1", "nodeType": "YulFunctionCall", "src": "9878:18:1"}, "nativeSrc": "9878:18:1", "nodeType": "YulExpressionStatement", "src": "9878:18:1"}]}, "condition": {"arguments": [{"name": "y", "nativeSrc": "9873:1:1", "nodeType": "YulIdentifier", "src": "9873:1:1"}], "functionName": {"name": "iszero", "nativeSrc": "9866:6:1", "nodeType": "YulIdentifier", "src": "9866:6:1"}, "nativeSrc": "9866:9:1", "nodeType": "YulFunctionCall", "src": "9866:9:1"}, "nativeSrc": "9863:35:1", "nodeType": "YulIf", "src": "9863:35:1"}, {"nativeSrc": "9908:14:1", "nodeType": "YulAssignment", "src": "9908:14:1", "value": {"arguments": [{"name": "x", "nativeSrc": "9917:1:1", "nodeType": "YulIdentifier", "src": "9917:1:1"}, {"name": "y", "nativeSrc": "9920:1:1", "nodeType": "YulIdentifier", "src": "9920:1:1"}], "functionName": {"name": "div", "nativeSrc": "9913:3:1", "nodeType": "YulIdentifier", "src": "9913:3:1"}, "nativeSrc": "9913:9:1", "nodeType": "YulFunctionCall", "src": "9913:9:1"}, "variableNames": [{"name": "r", "nativeSrc": "9908:1:1", "nodeType": "YulIdentifier", "src": "9908:1:1"}]}]}, "name": "checked_div_t_uint256", "nativeSrc": "9743:185:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nativeSrc": "9774:1:1", "nodeType": "YulTypedName", "src": "9774:1:1", "type": ""}, {"name": "y", "nativeSrc": "9777:1:1", "nodeType": "YulTypedName", "src": "9777:1:1", "type": ""}], "returnVariables": [{"name": "r", "nativeSrc": "9783:1:1", "nodeType": "YulTypedName", "src": "9783:1:1", "type": ""}], "src": "9743:185:1"}]}, "contents": "{\n\n    function array_length_t_array$_t_uint32_$dyn_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_array$_t_uint32_$dyn_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function array_dataslot_t_array$_t_uint32_$dyn_memory_ptr(ptr) -> data {\n        data := ptr\n\n        data := add(ptr, 0x20)\n\n    }\n\n    function cleanup_t_uint32(value) -> cleaned {\n        cleaned := and(value, 0xffffffff)\n    }\n\n    function abi_encode_t_uint32_to_t_uint32(value, pos) {\n        mstore(pos, cleanup_t_uint32(value))\n    }\n\n    function abi_encodeUpdatedPos_t_uint32_to_t_uint32(value0, pos) -> updatedPos {\n        abi_encode_t_uint32_to_t_uint32(value0, pos)\n        updatedPos := add(pos, 0x20)\n    }\n\n    function array_nextElement_t_array$_t_uint32_$dyn_memory_ptr(ptr) -> next {\n        next := add(ptr, 0x20)\n    }\n\n    // uint32[] -> uint32[]\n    function abi_encode_t_array$_t_uint32_$dyn_memory_ptr_to_t_array$_t_uint32_$dyn_memory_ptr_fromStack(value, pos)  -> end  {\n        let length := array_length_t_array$_t_uint32_$dyn_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_array$_t_uint32_$dyn_memory_ptr_fromStack(pos, length)\n        let baseRef := array_dataslot_t_array$_t_uint32_$dyn_memory_ptr(value)\n        let srcPtr := baseRef\n        for { let i := 0 } lt(i, length) { i := add(i, 1) }\n        {\n            let elementValue0 := mload(srcPtr)\n            pos := abi_encodeUpdatedPos_t_uint32_to_t_uint32(elementValue0, pos)\n            srcPtr := array_nextElement_t_array$_t_uint32_$dyn_memory_ptr(srcPtr)\n        }\n        end := pos\n    }\n\n    function array_length_t_array$_t_uint256_$dyn_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_array$_t_uint256_$dyn_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function array_dataslot_t_array$_t_uint256_$dyn_memory_ptr(ptr) -> data {\n        data := ptr\n\n        data := add(ptr, 0x20)\n\n    }\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function abi_encode_t_uint256_to_t_uint256(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encodeUpdatedPos_t_uint256_to_t_uint256(value0, pos) -> updatedPos {\n        abi_encode_t_uint256_to_t_uint256(value0, pos)\n        updatedPos := add(pos, 0x20)\n    }\n\n    function array_nextElement_t_array$_t_uint256_$dyn_memory_ptr(ptr) -> next {\n        next := add(ptr, 0x20)\n    }\n\n    // uint256[] -> uint256[]\n    function abi_encode_t_array$_t_uint256_$dyn_memory_ptr_to_t_array$_t_uint256_$dyn_memory_ptr_fromStack(value, pos)  -> end  {\n        let length := array_length_t_array$_t_uint256_$dyn_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_array$_t_uint256_$dyn_memory_ptr_fromStack(pos, length)\n        let baseRef := array_dataslot_t_array$_t_uint256_$dyn_memory_ptr(value)\n        let srcPtr := baseRef\n        for { let i := 0 } lt(i, length) { i := add(i, 1) }\n        {\n            let elementValue0 := mload(srcPtr)\n            pos := abi_encodeUpdatedPos_t_uint256_to_t_uint256(elementValue0, pos)\n            srcPtr := array_nextElement_t_array$_t_uint256_$dyn_memory_ptr(srcPtr)\n        }\n        end := pos\n    }\n\n    function abi_encode_tuple_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__to_t_array$_t_uint32_$dyn_memory_ptr_t_array$_t_uint256_$dyn_memory_ptr__fromStack_reversed(headStart , value1, value0) -> tail {\n        tail := add(headStart, 64)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_array$_t_uint32_$dyn_memory_ptr_to_t_array$_t_uint32_$dyn_memory_ptr_fromStack(value0,  tail)\n\n        mstore(add(headStart, 32), sub(tail, headStart))\n        tail := abi_encode_t_array$_t_uint256_$dyn_memory_ptr_to_t_array$_t_uint256_$dyn_memory_ptr_fromStack(value1,  tail)\n\n    }\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function validator_revert_t_uint32(value) {\n        if iszero(eq(value, cleanup_t_uint32(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint32(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint32(value)\n    }\n\n    function abi_decode_tuple_t_uint32(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint32(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_encode_t_uint256_to_t_uint256_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    function abi_encode_tuple_t_uint256__to_t_uint256__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint256_to_t_uint256_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function panic_error_0x41() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x41)\n        revert(0, 0x24)\n    }\n\n    function abi_encode_t_uint32_to_t_uint32_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint32(value))\n    }\n\n    function abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint32_to_t_uint32_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function array_length_t_bytes_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack(pos, length) -> updated_pos {\n        updated_pos := pos\n    }\n\n    function copy_memory_to_memory_with_cleanup(src, dst, length) {\n\n        let i := 0\n        for { } lt(i, length) { i := add(i, 32) }\n        {\n            mstore(add(dst, i), mload(add(src, i)))\n        }\n        mstore(add(dst, length), 0)\n\n    }\n\n    function abi_encode_t_bytes_memory_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack(value, pos) -> end {\n        let length := array_length_t_bytes_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_bytes_memory_ptr_nonPadded_inplace_fromStack(pos, length)\n        copy_memory_to_memory_with_cleanup(add(value, 0x20), pos, length)\n        end := add(pos, length)\n    }\n\n    function abi_encode_tuple_packed_t_bytes_memory_ptr__to_t_bytes_memory_ptr__nonPadded_inplace_fromStack_reversed(pos , value0) -> end {\n\n        pos := abi_encode_t_bytes_memory_ptr_to_t_bytes_memory_ptr_nonPadded_inplace_fromStack(value0,  pos)\n\n        end := pos\n    }\n\n    function cleanup_t_uint64(value) -> cleaned {\n        cleaned := and(value, 0xffffffffffffffff)\n    }\n\n    function validator_revert_t_uint64(value) {\n        if iszero(eq(value, cleanup_t_uint64(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint64_fromMemory(offset, end) -> value {\n        value := mload(offset)\n        validator_revert_t_uint64(value)\n    }\n\n    function abi_decode_tuple_t_uint64_fromMemory(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint64_fromMemory(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function panic_error_0x32() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x32)\n        revert(0, 0x24)\n    }\n\n    function panic_error_0x11() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x11)\n        revert(0, 0x24)\n    }\n\n    function increment_t_uint256(value) -> ret {\n        value := cleanup_t_uint256(value)\n        if eq(value, 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff) { panic_error_0x11() }\n        ret := add(value, 1)\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function store_literal_in_memory_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc(memPtr) {\n\n        mstore(add(memPtr, 0), \"Asset doesn't exist\")\n\n    }\n\n    function abi_encode_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc_to_t_string_memory_ptr_fromStack(pos) -> end {\n        pos := array_storeLengthForEncoding_t_string_memory_ptr_fromStack(pos, 19)\n        store_literal_in_memory_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc(pos)\n        end := add(pos, 32)\n    }\n\n    function abi_encode_tuple_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc__to_t_string_memory_ptr__fromStack_reversed(headStart ) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_stringliteral_b027b0accb5d15e948f866edf019c148e0906f538918a100e95861382b5d51dc_to_t_string_memory_ptr_fromStack( tail)\n\n    }\n\n    function panic_error_0x12() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x12)\n        revert(0, 0x24)\n    }\n\n    function checked_div_t_uint256(x, y) -> r {\n        x := cleanup_t_uint256(x)\n        y := cleanup_t_uint256(y)\n        if iszero(y) { panic_error_0x12() }\n\n        r := div(x, y)\n    }\n\n}\n", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x36 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x30320DD4 EQ PUSH2 0x3B JUMPI DUP1 PUSH4 0xDA26663A EQ PUSH2 0x5A JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x43 PUSH2 0x8A JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x51 SWAP3 SWAP2 SWAP1 PUSH2 0x637 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x74 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x6F SWAP2 SWAP1 PUSH2 0x69F JUMP JUMPDEST PUSH2 0x398 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x81 SWAP2 SWAP1 PUSH2 0x6DB JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH1 0x60 DUP1 PUSH1 0x0 PUSH1 0x14 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0xAA JUMPI PUSH2 0xA9 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0xD8 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP1 POP PUSH1 0x0 PUSH1 0x14 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0xF8 JUMPI PUSH2 0xF7 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x126 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP1 POP PUSH1 0x0 DUP1 JUMPDEST PUSH1 0x14 DUP2 PUSH4 0xFFFFFFFF AND LT ISZERO PUSH2 0x25F JUMPI PUSH1 0x0 DUP1 PUSH2 0x807 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP4 PUSH1 0x40 MLOAD PUSH1 0x20 ADD PUSH2 0x169 SWAP2 SWAP1 PUSH2 0x734 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 DUP4 SUB SUB DUP2 MSTORE SWAP1 PUSH1 0x40 MSTORE PUSH1 0x40 MLOAD PUSH2 0x185 SWAP2 SWAP1 PUSH2 0x7C0 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 GAS STATICCALL SWAP2 POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x1C0 JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x1C5 JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP SWAP2 POP SWAP2 POP DUP2 ISZERO PUSH2 0x250 JUMPI PUSH1 0x0 DUP2 DUP1 PUSH1 0x20 ADD SWAP1 MLOAD DUP2 ADD SWAP1 PUSH2 0x1E6 SWAP2 SWAP1 PUSH2 0x817 JUMP JUMPDEST SWAP1 POP DUP4 DUP8 DUP7 DUP2 MLOAD DUP2 LT PUSH2 0x1FC JUMPI PUSH2 0x1FB PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD SWAP1 PUSH4 0xFFFFFFFF AND SWAP1 DUP2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP7 DUP7 DUP2 MLOAD DUP2 LT PUSH2 0x234 JUMPI PUSH2 0x233 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD DUP2 DUP2 MSTORE POP POP DUP5 DUP1 PUSH2 0x24B SWAP1 PUSH2 0x8A2 JUMP JUMPDEST SWAP6 POP POP POP JUMPDEST POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0x12D JUMP JUMPDEST POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x27A JUMPI PUSH2 0x279 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x2A8 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP5 POP DUP1 PUSH8 0xFFFFFFFFFFFFFFFF DUP2 GT ISZERO PUSH2 0x2C5 JUMPI PUSH2 0x2C4 PUSH2 0x6F6 JUMP JUMPDEST JUMPDEST PUSH1 0x40 MLOAD SWAP1 DUP1 DUP3 MSTORE DUP1 PUSH1 0x20 MUL PUSH1 0x20 ADD DUP3 ADD PUSH1 0x40 MSTORE DUP1 ISZERO PUSH2 0x2F3 JUMPI DUP2 PUSH1 0x20 ADD PUSH1 0x20 DUP3 MUL DUP1 CALLDATASIZE DUP4 CALLDATACOPY DUP1 DUP3 ADD SWAP2 POP POP SWAP1 POP JUMPDEST POP SWAP4 POP PUSH1 0x0 JUMPDEST DUP2 DUP2 LT ISZERO PUSH2 0x390 JUMPI DUP4 DUP2 DUP2 MLOAD DUP2 LT PUSH2 0x314 JUMPI PUSH2 0x313 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD DUP7 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x32F JUMPI PUSH2 0x32E PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD SWAP1 PUSH4 0xFFFFFFFF AND SWAP1 DUP2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP POP DUP3 DUP2 DUP2 MLOAD DUP2 LT PUSH2 0x35C JUMPI PUSH2 0x35B PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD MLOAD DUP6 DUP3 DUP2 MLOAD DUP2 LT PUSH2 0x377 JUMPI PUSH2 0x376 PUSH2 0x844 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL PUSH1 0x20 ADD ADD DUP2 DUP2 MSTORE POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0x2F9 JUMP JUMPDEST POP POP POP POP SWAP1 SWAP2 JUMP JUMPDEST PUSH1 0x0 DUP1 PUSH1 0x0 PUSH2 0x807 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND DUP5 PUSH1 0x40 MLOAD PUSH1 0x20 ADD PUSH2 0x3C7 SWAP2 SWAP1 PUSH2 0x734 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH1 0x20 DUP2 DUP4 SUB SUB DUP2 MSTORE SWAP1 PUSH1 0x40 MSTORE PUSH1 0x40 MLOAD PUSH2 0x3E3 SWAP2 SWAP1 PUSH2 0x7C0 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP6 GAS STATICCALL SWAP2 POP POP RETURNDATASIZE DUP1 PUSH1 0x0 DUP2 EQ PUSH2 0x41E JUMPI PUSH1 0x40 MLOAD SWAP2 POP PUSH1 0x1F NOT PUSH1 0x3F RETURNDATASIZE ADD AND DUP3 ADD PUSH1 0x40 MSTORE RETURNDATASIZE DUP3 MSTORE RETURNDATASIZE PUSH1 0x0 PUSH1 0x20 DUP5 ADD RETURNDATACOPY PUSH2 0x423 JUMP JUMPDEST PUSH1 0x60 SWAP2 POP JUMPDEST POP SWAP2 POP SWAP2 POP DUP2 PUSH2 0x468 JUMPI PUSH1 0x40 MLOAD PUSH32 0x8C379A000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x45F SWAP1 PUSH2 0x947 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP2 DUP1 PUSH1 0x20 ADD SWAP1 MLOAD DUP2 ADD SWAP1 PUSH2 0x47E SWAP2 SWAP1 PUSH2 0x817 JUMP JUMPDEST SWAP1 POP PUSH1 0xA DUP2 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH2 0x497 SWAP2 SWAP1 PUSH2 0x996 JUMP JUMPDEST SWAP4 POP POP POP POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH4 0xFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x4E6 DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x4F8 DUP4 DUP4 PUSH2 0x4DD JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x51C DUP3 PUSH2 0x4A1 JUMP JUMPDEST PUSH2 0x526 DUP2 DUP6 PUSH2 0x4AC JUMP JUMPDEST SWAP4 POP PUSH2 0x531 DUP4 PUSH2 0x4BD JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x562 JUMPI DUP2 MLOAD PUSH2 0x549 DUP9 DUP3 PUSH2 0x4EC JUMP JUMPDEST SWAP8 POP PUSH2 0x554 DUP4 PUSH2 0x504 JUMP JUMPDEST SWAP3 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x535 JUMP JUMPDEST POP DUP6 SWAP4 POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x5AE DUP2 PUSH2 0x59B JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x5C0 DUP4 DUP4 PUSH2 0x5A5 JUMP JUMPDEST PUSH1 0x20 DUP4 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x5E4 DUP3 PUSH2 0x56F JUMP JUMPDEST PUSH2 0x5EE DUP2 DUP6 PUSH2 0x57A JUMP JUMPDEST SWAP4 POP PUSH2 0x5F9 DUP4 PUSH2 0x58B JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x62A JUMPI DUP2 MLOAD PUSH2 0x611 DUP9 DUP3 PUSH2 0x5B4 JUMP JUMPDEST SWAP8 POP PUSH2 0x61C DUP4 PUSH2 0x5CC JUMP JUMPDEST SWAP3 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x5FD JUMP JUMPDEST POP DUP6 SWAP4 POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x651 DUP2 DUP6 PUSH2 0x511 JUMP JUMPDEST SWAP1 POP DUP2 DUP2 SUB PUSH1 0x20 DUP4 ADD MSTORE PUSH2 0x665 DUP2 DUP5 PUSH2 0x5D9 JUMP JUMPDEST SWAP1 POP SWAP4 SWAP3 POP POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x67C DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP2 EQ PUSH2 0x687 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x699 DUP2 PUSH2 0x673 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x6B5 JUMPI PUSH2 0x6B4 PUSH2 0x66E JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x6C3 DUP5 DUP3 DUP6 ADD PUSH2 0x68A JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x6D5 DUP2 PUSH2 0x59B JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x6F0 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x6CC JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x72E DUP2 PUSH2 0x4CD JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x749 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x725 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x783 JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x768 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x79A DUP3 PUSH2 0x74F JUMP JUMPDEST PUSH2 0x7A4 DUP2 DUP6 PUSH2 0x75A JUMP JUMPDEST SWAP4 POP PUSH2 0x7B4 DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x765 JUMP JUMPDEST DUP1 DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7CC DUP3 DUP5 PUSH2 0x78F JUMP JUMPDEST SWAP2 POP DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x7F4 DUP2 PUSH2 0x7D7 JUMP JUMPDEST DUP2 EQ PUSH2 0x7FF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0x811 DUP2 PUSH2 0x7EB JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x82D JUMPI PUSH2 0x82C PUSH2 0x66E JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x83B DUP5 DUP3 DUP6 ADD PUSH2 0x802 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0x8AD DUP3 PUSH2 0x59B JUMP JUMPDEST SWAP2 POP PUSH32 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF DUP3 SUB PUSH2 0x8DF JUMPI PUSH2 0x8DE PUSH2 0x873 JUMP JUMPDEST JUMPDEST PUSH1 0x1 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x417373657420646F65736E277420657869737400000000000000000000000000 PUSH1 0x0 DUP3 ADD MSTORE POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x931 PUSH1 0x13 DUP4 PUSH2 0x8EA JUMP JUMPDEST SWAP2 POP PUSH2 0x93C DUP3 PUSH2 0x8FB JUMP JUMPDEST PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x960 DUP2 PUSH2 0x924 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x12 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0x9A1 DUP3 PUSH2 0x59B JUMP JUMPDEST SWAP2 POP PUSH2 0x9AC DUP4 PUSH2 0x59B JUMP JUMPDEST SWAP3 POP DUP3 PUSH2 0x9BC JUMPI PUSH2 0x9BB PUSH2 0x967 JUMP JUMPDEST JUMPDEST DUP3 DUP3 DIV SWAP1 POP SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 PUSH1 0x2A PUSH16 0x8538315CCA6449E49F19449D477B21D9 0xCE PUSH16 0x2F5B31796BF23AEE3A6A1064736F6C63 NUMBER STOP ADDMOD SHR STOP CALLER ", "sourceMap": "65:1477:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;140:970;;;:::i;:::-;;;;;;;;:::i;:::-;;;;;;;;1156:384;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;140:970;192:30;224:23;259:27;302:2;289:16;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;259:46;;315:24;353:2;342:14;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;315:41;;366:10;433:8;428:401;451:2;447:1;:6;;;428:401;;;475:12;489:19;520:42;512:62;;586:1;575:13;;;;;;;;:::i;:::-;;;;;;;;;;;;;512:77;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;474:115;;;;620:7;616:203;;;647:12;673:6;662:28;;;;;;;;;;;;:::i;:::-;647:43;;729:1;708:11;720:5;708:18;;;;;;;;:::i;:::-;;;;;;;:22;;;;;;;;;;;773:5;768:11;;748:10;759:5;748:17;;;;;;;;:::i;:::-;;;;;;;:31;;;;;797:7;;;;;:::i;:::-;;;;629:190;616:203;460:369;;455:3;;;;;;;428:401;;;;915:5;902:19;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;885:36;;954:5;943:17;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;931:29;;975:6;970:134;991:5;987:1;:9;970:134;;;1037:11;1049:1;1037:14;;;;;;;;:::i;:::-;;;;;;;;1017;1032:1;1017:17;;;;;;;;:::i;:::-;;;;;;;:34;;;;;;;;;;;1080:10;1091:1;1080:13;;;;;;;;:::i;:::-;;;;;;;;1065:9;1075:1;1065:12;;;;;;;;:::i;:::-;;;;;;;:28;;;;;998:3;;;;;;;970:134;;;;249:861;;;140:970;;:::o;1156:384::-;1216:4;1233:12;1247:19;1278:42;1270:62;;1344:10;1333:22;;;;;;;;:::i;:::-;;;;;;;;;;;;;1270:86;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1232:124;;;;1374:7;1366:39;;;;;;;;;;;;:::i;:::-;;;;;;;;;1424:15;1453:6;1442:28;;;;;;;;;;;;:::i;:::-;1424:46;;1504:2;1492:8;1487:14;;:19;;;;:::i;:::-;1480:26;;;;;1156:384;;;:::o;7:113:1:-;73:6;107:5;101:12;91:22;;7:113;;;:::o;126:183::-;224:11;258:6;253:3;246:19;298:4;293:3;289:14;274:29;;126:183;;;;:::o;315:131::-;381:4;404:3;396:11;;434:4;429:3;425:14;417:22;;315:131;;;:::o;452:93::-;488:7;528:10;521:5;517:22;506:33;;452:93;;;:::o;551:105::-;626:23;643:5;626:23;:::i;:::-;621:3;614:36;551:105;;:::o;662:175::-;729:10;750:44;790:3;782:6;750:44;:::i;:::-;826:4;821:3;817:14;803:28;;662:175;;;;:::o;843:112::-;912:4;944;939:3;935:14;927:22;;843:112;;;:::o;989:724::-;1106:3;1135:53;1182:5;1135:53;:::i;:::-;1204:85;1282:6;1277:3;1204:85;:::i;:::-;1197:92;;1313:55;1362:5;1313:55;:::i;:::-;1391:7;1422:1;1407:281;1432:6;1429:1;1426:13;1407:281;;;1508:6;1502:13;1535:61;1592:3;1577:13;1535:61;:::i;:::-;1528:68;;1619:59;1671:6;1619:59;:::i;:::-;1609:69;;1467:221;1454:1;1451;1447:9;1442:14;;1407:281;;;1411:14;1704:3;1697:10;;1111:602;;;989:724;;;;:::o;1719:114::-;1786:6;1820:5;1814:12;1804:22;;1719:114;;;:::o;1839:184::-;1938:11;1972:6;1967:3;1960:19;2012:4;2007:3;2003:14;1988:29;;1839:184;;;;:::o;2029:132::-;2096:4;2119:3;2111:11;;2149:4;2144:3;2140:14;2132:22;;2029:132;;;:::o;2167:77::-;2204:7;2233:5;2222:16;;2167:77;;;:::o;2250:108::-;2327:24;2345:5;2327:24;:::i;:::-;2322:3;2315:37;2250:108;;:::o;2364:179::-;2433:10;2454:46;2496:3;2488:6;2454:46;:::i;:::-;2532:4;2527:3;2523:14;2509:28;;2364:179;;;;:::o;2549:113::-;2619:4;2651;2646:3;2642:14;2634:22;;2549:113;;;:::o;2698:732::-;2817:3;2846:54;2894:5;2846:54;:::i;:::-;2916:86;2995:6;2990:3;2916:86;:::i;:::-;2909:93;;3026:56;3076:5;3026:56;:::i;:::-;3105:7;3136:1;3121:284;3146:6;3143:1;3140:13;3121:284;;;3222:6;3216:13;3249:63;3308:3;3293:13;3249:63;:::i;:::-;3242:70;;3335:60;3388:6;3335:60;:::i;:::-;3325:70;;3181:224;3168:1;3165;3161:9;3156:14;;3121:284;;;3125:14;3421:3;3414:10;;2822:608;;;2698:732;;;;:::o;3436:630::-;3655:4;3693:2;3682:9;3678:18;3670:26;;3742:9;3736:4;3732:20;3728:1;3717:9;3713:17;3706:47;3770:106;3871:4;3862:6;3770:106;:::i;:::-;3762:114;;3923:9;3917:4;3913:20;3908:2;3897:9;3893:18;3886:48;3951:108;4054:4;4045:6;3951:108;:::i;:::-;3943:116;;3436:630;;;;;:::o;4153:117::-;4262:1;4259;4252:12;4399:120;4471:23;4488:5;4471:23;:::i;:::-;4464:5;4461:34;4451:62;;4509:1;4506;4499:12;4451:62;4399:120;:::o;4525:137::-;4570:5;4608:6;4595:20;4586:29;;4624:32;4650:5;4624:32;:::i;:::-;4525:137;;;;:::o;4668:327::-;4726:6;4775:2;4763:9;4754:7;4750:23;4746:32;4743:119;;;4781:79;;:::i;:::-;4743:119;4901:1;4926:52;4970:7;4961:6;4950:9;4946:22;4926:52;:::i;:::-;4916:62;;4872:116;4668:327;;;;:::o;5001:118::-;5088:24;5106:5;5088:24;:::i;:::-;5083:3;5076:37;5001:118;;:::o;5125:222::-;5218:4;5256:2;5245:9;5241:18;5233:26;;5269:71;5337:1;5326:9;5322:17;5313:6;5269:71;:::i;:::-;5125:222;;;;:::o;5353:180::-;5401:77;5398:1;5391:88;5498:4;5495:1;5488:15;5522:4;5519:1;5512:15;5539:115;5624:23;5641:5;5624:23;:::i;:::-;5619:3;5612:36;5539:115;;:::o;5660:218::-;5751:4;5789:2;5778:9;5774:18;5766:26;;5802:69;5868:1;5857:9;5853:17;5844:6;5802:69;:::i;:::-;5660:218;;;;:::o;5884:98::-;5935:6;5969:5;5963:12;5953:22;;5884:98;;;:::o;5988:147::-;6089:11;6126:3;6111:18;;5988:147;;;;:::o;6141:248::-;6223:1;6233:113;6247:6;6244:1;6241:13;6233:113;;;6332:1;6327:3;6323:11;6317:18;6313:1;6308:3;6304:11;6297:39;6269:2;6266:1;6262:10;6257:15;;6233:113;;;6380:1;6371:6;6366:3;6362:16;6355:27;6203:186;6141:248;;;:::o;6395:386::-;6499:3;6527:38;6559:5;6527:38;:::i;:::-;6581:88;6662:6;6657:3;6581:88;:::i;:::-;6574:95;;6678:65;6736:6;6731:3;6724:4;6717:5;6713:16;6678:65;:::i;:::-;6768:6;6763:3;6759:16;6752:23;;6503:278;6395:386;;;;:::o;6787:271::-;6917:3;6939:93;7028:3;7019:6;6939:93;:::i;:::-;6932:100;;7049:3;7042:10;;6787:271;;;;:::o;7064:101::-;7100:7;7140:18;7133:5;7129:30;7118:41;;7064:101;;;:::o;7171:120::-;7243:23;7260:5;7243:23;:::i;:::-;7236:5;7233:34;7223:62;;7281:1;7278;7271:12;7223:62;7171:120;:::o;7297:141::-;7353:5;7384:6;7378:13;7369:22;;7400:32;7426:5;7400:32;:::i;:::-;7297:141;;;;:::o;7444:349::-;7513:6;7562:2;7550:9;7541:7;7537:23;7533:32;7530:119;;;7568:79;;:::i;:::-;7530:119;7688:1;7713:63;7768:7;7759:6;7748:9;7744:22;7713:63;:::i;:::-;7703:73;;7659:127;7444:349;;;;:::o;7799:180::-;7847:77;7844:1;7837:88;7944:4;7941:1;7934:15;7968:4;7965:1;7958:15;7985:180;8033:77;8030:1;8023:88;8130:4;8127:1;8120:15;8154:4;8151:1;8144:15;8171:233;8210:3;8233:24;8251:5;8233:24;:::i;:::-;8224:33;;8279:66;8272:5;8269:77;8266:103;;8349:18;;:::i;:::-;8266:103;8396:1;8389:5;8385:13;8378:20;;8171:233;;;:::o;8410:169::-;8494:11;8528:6;8523:3;8516:19;8568:4;8563:3;8559:14;8544:29;;8410:169;;;;:::o;8585:::-;8725:21;8721:1;8713:6;8709:14;8702:45;8585:169;:::o;8760:366::-;8902:3;8923:67;8987:2;8982:3;8923:67;:::i;:::-;8916:74;;8999:93;9088:3;8999:93;:::i;:::-;9117:2;9112:3;9108:12;9101:19;;8760:366;;;:::o;9132:419::-;9298:4;9336:2;9325:9;9321:18;9313:26;;9385:9;9379:4;9375:20;9371:1;9360:9;9356:17;9349:47;9413:131;9539:4;9413:131;:::i;:::-;9405:139;;9132:419;;;:::o;9557:180::-;9605:77;9602:1;9595:88;9702:4;9699:1;9692:15;9726:4;9723:1;9716:15;9743:185;9783:1;9800:20;9818:1;9800:20;:::i;:::-;9795:25;;9834:20;9852:1;9834:20;:::i;:::-;9829:25;;9873:1;9863:35;;9878:18;;:::i;:::-;9863:35;9920:1;9917;9913:9;9908:14;;9743:185;;;;:::o"}, "methodIdentifiers": {"findWorkingAssets()": "30320dd4", "getPrice(uint32)": "da26663a"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"findWorkingAssets\",\"outputs\":[{\"internalType\":\"uint32[]\",\"name\":\"workingIndices\",\"type\":\"uint32[]\"},{\"internalType\":\"uint256[]\",\"name\":\"rawPrices\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"assetIndex\",\"type\":\"uint32\"}],\"name\":\"getPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/Please.sol\":\"Please\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/Please.sol\":{\"keccak256\":\"0x42140f46fdb5383c7b4294b8218ffec3663340213143e5315ce6268524d4e328\",\"license\":\"UNLICENSED\",\"urls\":[\"bzz-raw://17c0939760243b0b4ed74da193c9c12b9bf6af188ad83fb0595af3b732fbb330\",\"dweb:/ipfs/Qmdc3ts2EXxPnedjqBp8PpfYN4wZJaAVXRLTLE4VEySXsx\"]}},\"version\":1}"}}}}}