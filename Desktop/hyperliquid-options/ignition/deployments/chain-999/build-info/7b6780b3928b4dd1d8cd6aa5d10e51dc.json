{"id": "7b6780b3928b4dd1d8cd6aa5d10e51dc", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.28", "solcLongVersion": "0.8.28+commit.7893614a", "input": {"language": "Solidity", "sources": {"contracts/ReadPrices.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.0;\n\ninterface IL1Read {\n    function oraclePx(uint32 index) external view returns (uint64);\n    function l1BlockNumber() external view returns (uint32);\n}\n\ncontract ReadPrices {\n    IL1Read constant l1Read = IL1Read(0x0000000000000000000000000000000000000800); // L1 is a precompile deployed on the Hyperliquid Testnet\n    \n    struct Price {\n        string symbol;\n        uint32 assetId;\n        uint64 rawPrice;\n        uint256 formattedPrice; \n        uint32 blockNumber;\n    }\n    \n    function getPrice(uint32 assetId) external view returns (Price memory) {\n        uint64 rawPrice = l1Read.oraclePx(assetId);\n        return Price({\n            symbol: getSymbol(assetId),\n            assetId: assetId,\n            rawPrice: rawPrice,\n            formattedPrice: formatPrice(rawPrice),\n            blockNumber: l1Read.l1BlockNumber()\n        });\n    }\n    \n    function getAllPrices() external view returns (Price[5] memory) {\n        Price[5] memory prices;\n        uint32[5] memory assets = [uint32(0), 1, 2, 3, 4]; // BTC, ETH, SOL, AVAX, HYPE\n        \n        for (uint i = 0; i < 5; i++) {\n            uint64 rawPrice = l1Read.oraclePx(assets[i]);\n            prices[i] = Price({\n                symbol: getSymbol(assets[i]),\n                assetId: assets[i],\n                rawPrice: rawPrice,\n                formattedPrice: formatPrice(rawPrice),\n                blockNumber: l1Read.l1BlockNumber()\n            });\n        }\n        return prices;\n    }\n    \n    function formatPrice(uint64 rawPrice) internal pure returns (uint256) {\n        return uint256(rawPrice) / 100;\n    }\n    \n    function getSymbol(uint32 assetId) internal pure returns (string memory) {\n        if (assetId == 0) return \"BTC\";\n        if (assetId == 1) return \"ETH\";\n        if (assetId == 2) return \"SOL\";\n        if (assetId == 3) return \"AVAX\";\n        if (assetId == 4) return \"HYPE\";\n        return \"UNKNOWN\";\n    }\n}"}}, "settings": {"evmVersion": "paris", "optimizer": {"enabled": false, "runs": 200}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"contracts/ReadPrices.sol": {"ast": {"absolutePath": "contracts/ReadPrices.sol", "exportedSymbols": {"IL1Read": [14], "ReadPrices": [194]}, "id": 195, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".0"], "nodeType": "PragmaDirective", "src": "32:23:0"}, {"abstract": false, "baseContracts": [], "canonicalName": "IL1Read", "contractDependencies": [], "contractKind": "interface", "fullyImplemented": false, "id": 14, "linearizedBaseContracts": [14], "name": "IL1Read", "nameLocation": "67:7:0", "nodeType": "ContractDefinition", "nodes": [{"functionSelector": "c0f0f5ef", "id": 8, "implemented": false, "kind": "function", "modifiers": [], "name": "oraclePx", "nameLocation": "90:8:0", "nodeType": "FunctionDefinition", "parameters": {"id": 4, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3, "mutability": "mutable", "name": "index", "nameLocation": "106:5:0", "nodeType": "VariableDeclaration", "scope": 8, "src": "99:12:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 2, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "99:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "98:14:0"}, "returnParameters": {"id": 7, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 6, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 8, "src": "136:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 5, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "136:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "135:8:0"}, "scope": 14, "src": "81:63:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"functionSelector": "298c9005", "id": 13, "implemented": false, "kind": "function", "modifiers": [], "name": "l1BlockNumber", "nameLocation": "158:13:0", "nodeType": "FunctionDefinition", "parameters": {"id": 9, "nodeType": "ParameterList", "parameters": [], "src": "171:2:0"}, "returnParameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 13, "src": "197:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 10, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "197:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "196:8:0"}, "scope": 14, "src": "149:56:0", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 195, "src": "57:150:0", "usedErrors": [], "usedEvents": []}, {"abstract": false, "baseContracts": [], "canonicalName": "ReadPrices", "contractDependencies": [], "contractKind": "contract", "fullyImplemented": true, "id": 194, "linearizedBaseContracts": [194], "name": "ReadPrices", "nameLocation": "218:10:0", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 20, "mutability": "constant", "name": "l1Read", "nameLocation": "252:6:0", "nodeType": "VariableDeclaration", "scope": 194, "src": "235:77:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}, "typeName": {"id": 16, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 15, "name": "IL1Read", "nameLocations": ["235:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 14, "src": "235:7:0"}, "referencedDeclaration": 14, "src": "235:7:0", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "value": {"arguments": [{"hexValue": "307830303030303030303030303030303030303030303030303030303030303030303030303030383030", "id": 18, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "269:42:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "value": "0x0000000000000000000000000000000000000800"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 17, "name": "IL1Read", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 14, "src": "261:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IL1Read_$14_$", "typeString": "type(contract IL1Read)"}}, "id": 19, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "261:51:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "visibility": "internal"}, {"canonicalName": "ReadPrices.Price", "id": 31, "members": [{"constant": false, "id": 22, "mutability": "mutable", "name": "symbol", "nameLocation": "411:6:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "404:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 21, "name": "string", "nodeType": "ElementaryTypeName", "src": "404:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}, {"constant": false, "id": 24, "mutability": "mutable", "name": "assetId", "nameLocation": "434:7:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "427:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 23, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "427:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}, {"constant": false, "id": 26, "mutability": "mutable", "name": "rawPrice", "nameLocation": "458:8:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "451:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 25, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "451:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}, {"constant": false, "id": 28, "mutability": "mutable", "name": "formattedPrice", "nameLocation": "484:14:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "476:22:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 27, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "476:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 30, "mutability": "mutable", "name": "blockNumber", "nameLocation": "516:11:0", "nodeType": "VariableDeclaration", "scope": 31, "src": "509:18:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 29, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "509:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "name": "Price", "nameLocation": "388:5:0", "nodeType": "StructDefinition", "scope": 194, "src": "381:153:0", "visibility": "public"}, {"body": {"id": 60, "nodeType": "Block", "src": "615:295:0", "statements": [{"assignments": [40], "declarations": [{"constant": false, "id": 40, "mutability": "mutable", "name": "rawPrice", "nameLocation": "632:8:0", "nodeType": "VariableDeclaration", "scope": 60, "src": "625:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 39, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "625:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 45, "initialValue": {"arguments": [{"id": 43, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "659:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "expression": {"id": 41, "name": "l1Read", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "643:6:0", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "id": 42, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "650:8:0", "memberName": "oraclePx", "nodeType": "MemberAccess", "referencedDeclaration": 8, "src": "643:15:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_uint32_$returns$_t_uint64_$", "typeString": "function (uint32) view external returns (uint64)"}}, "id": 44, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "643:24:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "625:42:0"}, {"expression": {"arguments": [{"arguments": [{"id": 48, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "722:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "id": 47, "name": "getSymbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "712:9:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint32_$returns$_t_string_memory_ptr_$", "typeString": "function (uint32) pure returns (string memory)"}}, "id": 49, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "712:18:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"id": 50, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 33, "src": "753:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, {"id": 51, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40, "src": "784:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, {"arguments": [{"id": 53, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 40, "src": "834:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 52, "name": "formatPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 153, "src": "822:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint64_$returns$_t_uint256_$", "typeString": "function (uint64) pure returns (uint256)"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "822:21:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 55, "name": "l1Read", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "870:6:0", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "id": 56, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "877:13:0", "memberName": "l1BlockNumber", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "870:20:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint32_$", "typeString": "function () view external returns (uint32)"}}, "id": 57, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "870:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint32", "typeString": "uint32"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint32", "typeString": "uint32"}], "id": 46, "name": "Price", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "684:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Price_$31_storage_ptr_$", "typeString": "type(struct ReadPrices.Price storage pointer)"}}, "id": 58, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["704:6:0", "744:7:0", "774:8:0", "806:14:0", "857:11:0"], "names": ["symbol", "assetId", "rawPrice", "formattedPrice", "blockNumber"], "nodeType": "FunctionCall", "src": "684:219:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_memory_ptr", "typeString": "struct ReadPrices.Price memory"}}, "functionReturnParameters": 38, "id": 59, "nodeType": "Return", "src": "677:226:0"}]}, "functionSelector": "da26663a", "id": 61, "implemented": true, "kind": "function", "modifiers": [], "name": "getPrice", "nameLocation": "553:8:0", "nodeType": "FunctionDefinition", "parameters": {"id": 34, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 33, "mutability": "mutable", "name": "assetId", "nameLocation": "569:7:0", "nodeType": "VariableDeclaration", "scope": 61, "src": "562:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 32, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "562:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "561:16:0"}, "returnParameters": {"id": 38, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 37, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 61, "src": "601:12:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_memory_ptr", "typeString": "struct ReadPrices.Price"}, "typeName": {"id": 36, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 35, "name": "Price", "nameLocations": ["601:5:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 31, "src": "601:5:0"}, "referencedDeclaration": 31, "src": "601:5:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_storage_ptr", "typeString": "struct ReadPrices.Price"}}, "visibility": "internal"}], "src": "600:14:0"}, "scope": 194, "src": "544:366:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 137, "nodeType": "Block", "src": "984:539:0", "statements": [{"assignments": [74], "declarations": [{"constant": false, "id": 74, "mutability": "mutable", "name": "prices", "nameLocation": "1010:6:0", "nodeType": "VariableDeclaration", "scope": 137, "src": "994:22:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "typeString": "struct ReadPrices.Price[5]"}, "typeName": {"baseType": {"id": 72, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 71, "name": "Price", "nameLocations": ["994:5:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 31, "src": "994:5:0"}, "referencedDeclaration": 31, "src": "994:5:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_storage_ptr", "typeString": "struct ReadPrices.Price"}}, "id": 73, "length": {"hexValue": "35", "id": 70, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1000:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}, "nodeType": "ArrayTypeName", "src": "994:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_storage_$5_storage_ptr", "typeString": "struct ReadPrices.Price[5]"}}, "visibility": "internal"}], "id": 75, "nodeType": "VariableDeclarationStatement", "src": "994:22:0"}, {"assignments": [81], "declarations": [{"constant": false, "id": 81, "mutability": "mutable", "name": "assets", "nameLocation": "1043:6:0", "nodeType": "VariableDeclaration", "scope": 137, "src": "1026:23:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_memory_ptr", "typeString": "uint32[5]"}, "typeName": {"baseType": {"id": 79, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "1026:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "id": 80, "length": {"hexValue": "35", "id": 78, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1033:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}, "nodeType": "ArrayTypeName", "src": "1026:9:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_storage_ptr", "typeString": "uint32[5]"}}, "visibility": "internal"}], "id": 91, "initialValue": {"components": [{"arguments": [{"hexValue": "30", "id": 84, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1060:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 83, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1053:6:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint32_$", "typeString": "type(uint32)"}, "typeName": {"id": 82, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "1053:6:0", "typeDescriptions": {}}}, "id": 85, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1053:9:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, {"hexValue": "31", "id": 86, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1064:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, {"hexValue": "32", "id": 87, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1067:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, {"hexValue": "33", "id": 88, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1070:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}, {"hexValue": "34", "id": 89, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1073:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}], "id": 90, "isConstant": false, "isInlineArray": true, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "1052:23:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_memory_ptr", "typeString": "uint32[5] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "1026:49:0"}, {"body": {"id": 133, "nodeType": "Block", "src": "1152:342:0", "statements": [{"assignments": [103], "declarations": [{"constant": false, "id": 103, "mutability": "mutable", "name": "rawPrice", "nameLocation": "1173:8:0", "nodeType": "VariableDeclaration", "scope": 133, "src": "1166:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 102, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1166:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "id": 110, "initialValue": {"arguments": [{"baseExpression": {"id": 106, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "1200:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_memory_ptr", "typeString": "uint32[5] memory"}}, "id": 108, "indexExpression": {"id": 107, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1207:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1200:9:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "expression": {"id": 104, "name": "l1Read", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "1184:6:0", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "id": 105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1191:8:0", "memberName": "oraclePx", "nodeType": "MemberAccess", "referencedDeclaration": 8, "src": "1184:15:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_uint32_$returns$_t_uint64_$", "typeString": "function (uint32) view external returns (uint64)"}}, "id": 109, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1184:26:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "VariableDeclarationStatement", "src": "1166:44:0"}, {"expression": {"id": 131, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 111, "name": "prices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 74, "src": "1224:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "typeString": "struct ReadPrices.Price memory[5] memory"}}, "id": 113, "indexExpression": {"id": 112, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1231:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1224:9:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_memory_ptr", "typeString": "struct ReadPrices.Price memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"baseExpression": {"id": 116, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "1278:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_memory_ptr", "typeString": "uint32[5] memory"}}, "id": 118, "indexExpression": {"id": 117, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1285:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1278:9:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint32", "typeString": "uint32"}], "id": 115, "name": "getSymbol", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 193, "src": "1268:9:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint32_$returns$_t_string_memory_ptr_$", "typeString": "function (uint32) pure returns (string memory)"}}, "id": 119, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1268:20:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"baseExpression": {"id": 120, "name": "assets", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 81, "src": "1315:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint32_$5_memory_ptr", "typeString": "uint32[5] memory"}}, "id": 122, "indexExpression": {"id": 121, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1322:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1315:9:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, {"id": 123, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103, "src": "1352:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, {"arguments": [{"id": 125, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 103, "src": "1406:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 124, "name": "formatPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 153, "src": "1394:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint64_$returns$_t_uint256_$", "typeString": "function (uint64) pure returns (uint256)"}}, "id": 126, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1394:21:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"arguments": [], "expression": {"argumentTypes": [], "expression": {"id": 127, "name": "l1Read", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 20, "src": "1446:6:0", "typeDescriptions": {"typeIdentifier": "t_contract$_IL1Read_$14", "typeString": "contract IL1Read"}}, "id": 128, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "1453:13:0", "memberName": "l1BlockNumber", "nodeType": "MemberAccess", "referencedDeclaration": 13, "src": "1446:20:0", "typeDescriptions": {"typeIdentifier": "t_function_external_view$__$returns$_t_uint32_$", "typeString": "function () view external returns (uint32)"}}, "id": 129, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1446:22:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_uint32", "typeString": "uint32"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint32", "typeString": "uint32"}], "id": 114, "name": "Price", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "1236:5:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Price_$31_storage_ptr_$", "typeString": "type(struct ReadPrices.Price storage pointer)"}}, "id": 130, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["1260:6:0", "1306:7:0", "1342:8:0", "1378:14:0", "1433:11:0"], "names": ["symbol", "assetId", "rawPrice", "formattedPrice", "blockNumber"], "nodeType": "FunctionCall", "src": "1236:247:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_memory_ptr", "typeString": "struct ReadPrices.Price memory"}}, "src": "1224:259:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_memory_ptr", "typeString": "struct ReadPrices.Price memory"}}, "id": 132, "nodeType": "ExpressionStatement", "src": "1224:259:0"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 98, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 96, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1140:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"hexValue": "35", "id": 97, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1144:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}, "src": "1140:5:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 134, "initializationExpression": {"assignments": [93], "declarations": [{"constant": false, "id": 93, "mutability": "mutable", "name": "i", "nameLocation": "1133:1:0", "nodeType": "VariableDeclaration", "scope": 134, "src": "1128:6:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 92, "name": "uint", "nodeType": "ElementaryTypeName", "src": "1128:4:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 95, "initialValue": {"hexValue": "30", "id": 94, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1137:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "1128:10:0"}, "isSimpleCounterLoop": true, "loopExpression": {"expression": {"id": 100, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1147:3:0", "subExpression": {"id": 99, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 93, "src": "1147:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 101, "nodeType": "ExpressionStatement", "src": "1147:3:0"}, "nodeType": "ForStatement", "src": "1123:371:0"}, {"expression": {"id": 135, "name": "prices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 74, "src": "1510:6:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "typeString": "struct ReadPrices.Price memory[5] memory"}}, "functionReturnParameters": 68, "id": 136, "nodeType": "Return", "src": "1503:13:0"}]}, "functionSelector": "445df9d6", "id": 138, "implemented": true, "kind": "function", "modifiers": [], "name": "getAllPrices", "nameLocation": "929:12:0", "nodeType": "FunctionDefinition", "parameters": {"id": 62, "nodeType": "ParameterList", "parameters": [], "src": "941:2:0"}, "returnParameters": {"id": 68, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 67, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 138, "src": "967:15:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "typeString": "struct ReadPrices.Price[5]"}, "typeName": {"baseType": {"id": 64, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 63, "name": "Price", "nameLocations": ["967:5:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 31, "src": "967:5:0"}, "referencedDeclaration": 31, "src": "967:5:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Price_$31_storage_ptr", "typeString": "struct ReadPrices.Price"}}, "id": 66, "length": {"hexValue": "35", "id": 65, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "973:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_5_by_1", "typeString": "int_const 5"}, "value": "5"}, "nodeType": "ArrayTypeName", "src": "967:8:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Price_$31_storage_$5_storage_ptr", "typeString": "struct ReadPrices.Price[5]"}}, "visibility": "internal"}], "src": "966:17:0"}, "scope": 194, "src": "920:603:0", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 152, "nodeType": "Block", "src": "1603:47:0", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 150, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [{"id": 147, "name": "rawPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 140, "src": "1628:8:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint64", "typeString": "uint64"}], "id": 146, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1620:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 145, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1620:7:0", "typeDescriptions": {}}}, "id": 148, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1620:17:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"hexValue": "313030", "id": 149, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1640:3:0", "typeDescriptions": {"typeIdentifier": "t_rational_100_by_1", "typeString": "int_const 100"}, "value": "100"}, "src": "1620:23:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 144, "id": 151, "nodeType": "Return", "src": "1613:30:0"}]}, "id": 153, "implemented": true, "kind": "function", "modifiers": [], "name": "formatPrice", "nameLocation": "1542:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 141, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 140, "mutability": "mutable", "name": "rawPrice", "nameLocation": "1561:8:0", "nodeType": "VariableDeclaration", "scope": 153, "src": "1554:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 139, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1554:6:0", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}], "src": "1553:17:0"}, "returnParameters": {"id": 144, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 143, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 153, "src": "1594:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 142, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1594:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1593:9:0"}, "scope": 194, "src": "1533:117:0", "stateMutability": "pure", "virtual": false, "visibility": "internal"}, {"body": {"id": 192, "nodeType": "Block", "src": "1733:235:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 162, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 160, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 155, "src": "1747:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "30", "id": 161, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1758:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "1747:12:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 165, "nodeType": "IfStatement", "src": "1743:30:0", "trueBody": {"expression": {"hexValue": "425443", "id": 163, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1768:5:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_e98e2830be1a7e4156d656a7505e65d08c67660dc618072422e9c78053c261e9", "typeString": "literal_string \"BTC\""}, "value": "BTC"}, "functionReturnParameters": 159, "id": 164, "nodeType": "Return", "src": "1761:12:0"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 168, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 166, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 155, "src": "1787:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "31", "id": 167, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1798:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "1787:12:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 171, "nodeType": "IfStatement", "src": "1783:30:0", "trueBody": {"expression": {"hexValue": "455448", "id": 169, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1808:5:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_aaaebeba3810b1e6b70781f14b2d72c1cb89c0b2b320c43bb67ff79f562f5ff4", "typeString": "literal_string \"ETH\""}, "value": "ETH"}, "functionReturnParameters": 159, "id": 170, "nodeType": "Return", "src": "1801:12:0"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 174, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 172, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 155, "src": "1827:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "32", "id": 173, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1838:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "src": "1827:12:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 177, "nodeType": "IfStatement", "src": "1823:30:0", "trueBody": {"expression": {"hexValue": "534f4c", "id": 175, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1848:5:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0a3ec4fc70eaf64faf6eeda4e9b2bd4742a785464053aa23afad8bd24650e86f", "typeString": "literal_string \"SOL\""}, "value": "SOL"}, "functionReturnParameters": 159, "id": 176, "nodeType": "Return", "src": "1841:12:0"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 180, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 178, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 155, "src": "1867:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "33", "id": 179, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1878:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_3_by_1", "typeString": "int_const 3"}, "value": "3"}, "src": "1867:12:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 183, "nodeType": "IfStatement", "src": "1863:31:0", "trueBody": {"expression": {"hexValue": "41564158", "id": 181, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1888:6:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_49f18ed70c3220abb735efdc7fce309e384d008d4bc14c846dcce9e31050e29b", "typeString": "literal_string \"AVAX\""}, "value": "AVAX"}, "functionReturnParameters": 159, "id": 182, "nodeType": "Return", "src": "1881:13:0"}}, {"condition": {"commonType": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 184, "name": "assetId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 155, "src": "1908:7:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"hexValue": "34", "id": 185, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1919:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_4_by_1", "typeString": "int_const 4"}, "value": "4"}, "src": "1908:12:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 189, "nodeType": "IfStatement", "src": "1904:31:0", "trueBody": {"expression": {"hexValue": "48595045", "id": 187, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1929:6:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_a8631acbe37917ccaaf28385e6997f2db9e3876151964f689ee4762ac40f71b8", "typeString": "literal_string \"HYPE\""}, "value": "HYPE"}, "functionReturnParameters": 159, "id": 188, "nodeType": "Return", "src": "1922:13:0"}}, {"expression": {"hexValue": "554e4b4e4f574e", "id": 190, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1952:9:0", "typeDescriptions": {"typeIdentifier": "t_stringliteral_7695161cac249b85fbc07184e7834e478da25ac93990ab166c1c538e6504e47e", "typeString": "literal_string \"UNKNOWN\""}, "value": "UNKNOWN"}, "functionReturnParameters": 159, "id": 191, "nodeType": "Return", "src": "1945:16:0"}]}, "id": 193, "implemented": true, "kind": "function", "modifiers": [], "name": "getSymbol", "nameLocation": "1669:9:0", "nodeType": "FunctionDefinition", "parameters": {"id": 156, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 155, "mutability": "mutable", "name": "assetId", "nameLocation": "1686:7:0", "nodeType": "VariableDeclaration", "scope": 193, "src": "1679:14:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}, "typeName": {"id": 154, "name": "uint32", "nodeType": "ElementaryTypeName", "src": "1679:6:0", "typeDescriptions": {"typeIdentifier": "t_uint32", "typeString": "uint32"}}, "visibility": "internal"}], "src": "1678:16:0"}, "returnParameters": {"id": 159, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 158, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 193, "src": "1718:13:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 157, "name": "string", "nodeType": "ElementaryTypeName", "src": "1718:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "visibility": "internal"}], "src": "1717:15:0"}, "scope": 194, "src": "1660:308:0", "stateMutability": "pure", "virtual": false, "visibility": "internal"}], "scope": 195, "src": "209:1761:0", "usedErrors": [], "usedEvents": []}], "src": "32:1938:0"}, "id": 0}}, "contracts": {"contracts/ReadPrices.sol": {"IL1Read": {"abi": [{"inputs": [], "name": "l1BlockNumber", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "index", "type": "uint32"}], "name": "oraclePx", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"l1BlockNumber()": "298c9005", "oraclePx(uint32)": "c0f0f5ef"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"l1BlockNumber\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"index\",\"type\":\"uint32\"}],\"name\":\"oraclePx\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/ReadPrices.sol\":\"IL1Read\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/ReadPrices.sol\":{\"keccak256\":\"0xe8e6e02c0c07822ef8d599de78042ec69fe3255f291b442ed99a1ddf029dbc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8a47e816c5e5ad4a7a7fa51025c14d5e26db23bf299f3146daac9522c56ee2d9\",\"dweb:/ipfs/QmfBdHkFsxnBiqn3mx76KLPe42uSdBY8K6kUZrqZXPsxRr\"]}},\"version\":1}"}, "ReadPrices": {"abi": [{"inputs": [], "name": "getAllPrices", "outputs": [{"components": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint32", "name": "assetId", "type": "uint32"}, {"internalType": "uint64", "name": "rawPrice", "type": "uint64"}, {"internalType": "uint256", "name": "formattedPrice", "type": "uint256"}, {"internalType": "uint32", "name": "blockNumber", "type": "uint32"}], "internalType": "struct ReadPrices.Price[5]", "name": "", "type": "tuple[5]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "assetId", "type": "uint32"}], "name": "getPrice", "outputs": [{"components": [{"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint32", "name": "assetId", "type": "uint32"}, {"internalType": "uint64", "name": "rawPrice", "type": "uint64"}, {"internalType": "uint256", "name": "formattedPrice", "type": "uint256"}, {"internalType": "uint32", "name": "blockNumber", "type": "uint32"}], "internalType": "struct ReadPrices.Price", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH1 0xF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH2 0xAFC DUP1 PUSH2 0x1F PUSH1 0x0 CODECOPY PUSH1 0x0 RETURN INVALID PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x36 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x445DF9D6 EQ PUSH2 0x3B JUMPI DUP1 PUSH4 0xDA26663A EQ PUSH2 0x59 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x43 PUSH2 0x89 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x50 SWAP2 SWAP1 PUSH2 0x85A JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x73 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x6E SWAP2 SWAP1 PUSH2 0x8AD JUMP JUMPDEST PUSH2 0x2AD JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x80 SWAP2 SWAP1 PUSH2 0x950 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x91 PUSH2 0x5D1 JUMP JUMPDEST PUSH2 0x99 PUSH2 0x5D1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x0 PUSH4 0xFFFFFFFF AND PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x1 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x3 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x4 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP1 POP PUSH1 0x0 JUMPDEST PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x2A4 JUMPI PUSH1 0x0 PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0xC0F0F5EF DUP5 DUP5 PUSH1 0x5 DUP2 LT PUSH2 0x12C JUMPI PUSH2 0x12B PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH1 0x40 MLOAD DUP3 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x14D SWAP2 SWAP1 PUSH2 0x9B0 JUMP JUMPDEST PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x16A JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x18E SWAP2 SWAP1 PUSH2 0x9F7 JUMP JUMPDEST SWAP1 POP PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH2 0x1BB DUP6 DUP6 PUSH1 0x5 DUP2 LT PUSH2 0x1B1 JUMPI PUSH2 0x1B0 PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH2 0x3FA JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD DUP5 DUP5 PUSH1 0x5 DUP2 LT PUSH2 0x1D3 JUMPI PUSH2 0x1D2 PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x1FC DUP4 PUSH2 0x5B1 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0x298C9005 PUSH1 0x40 MLOAD DUP2 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x24E JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x272 SWAP2 SWAP1 PUSH2 0xA39 JUMP JUMPDEST PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP DUP5 DUP4 PUSH1 0x5 DUP2 LT PUSH2 0x28E JUMPI PUSH2 0x28D PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD DUP2 SWAP1 MSTORE POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0xF0 JUMP JUMPDEST POP DUP2 SWAP3 POP POP POP SWAP1 JUMP JUMPDEST PUSH2 0x2B5 PUSH2 0x5FE JUMP JUMPDEST PUSH1 0x0 PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0xC0F0F5EF DUP5 PUSH1 0x40 MLOAD DUP3 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x2F2 SWAP2 SWAP1 PUSH2 0x9B0 JUMP JUMPDEST PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x30F JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x333 SWAP2 SWAP1 PUSH2 0x9F7 JUMP JUMPDEST SWAP1 POP PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH2 0x349 DUP6 PUSH2 0x3FA JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD DUP5 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x373 DUP4 PUSH2 0x5B1 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0x298C9005 PUSH1 0x40 MLOAD DUP2 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x3C5 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x3E9 SWAP2 SWAP1 PUSH2 0xA39 JUMP JUMPDEST PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP2 POP POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x60 PUSH1 0x0 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x447 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4254430000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x1 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x492 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4554480000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x2 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x4DD JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x534F4C0000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x3 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x528 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x4 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4156415800000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x4 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x573 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x4 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4859504500000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x7 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x554E4B4E4F574E00000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x64 DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH2 0x5CA SWAP2 SWAP1 PUSH2 0xA95 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 SWAP1 JUMPDEST PUSH2 0x5E8 PUSH2 0x5FE JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 SWAP1 SUB SWAP1 DUP2 PUSH2 0x5E0 JUMPI SWAP1 POP POP SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x60 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x5 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x69D JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x682 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x6C5 DUP3 PUSH2 0x663 JUMP JUMPDEST PUSH2 0x6CF DUP2 DUP6 PUSH2 0x66E JUMP JUMPDEST SWAP4 POP PUSH2 0x6DF DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x67F JUMP JUMPDEST PUSH2 0x6E8 DUP2 PUSH2 0x6A9 JUMP JUMPDEST DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH4 0xFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x70C DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x72F DUP2 PUSH2 0x712 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x748 DUP2 PUSH2 0x735 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0xA0 DUP4 ADD PUSH1 0x0 DUP4 ADD MLOAD DUP5 DUP3 SUB PUSH1 0x0 DUP7 ADD MSTORE PUSH2 0x76B DUP3 DUP3 PUSH2 0x6BA JUMP JUMPDEST SWAP2 POP POP PUSH1 0x20 DUP4 ADD MLOAD PUSH2 0x780 PUSH1 0x20 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP PUSH1 0x40 DUP4 ADD MLOAD PUSH2 0x793 PUSH1 0x40 DUP7 ADD DUP3 PUSH2 0x726 JUMP JUMPDEST POP PUSH1 0x60 DUP4 ADD MLOAD PUSH2 0x7A6 PUSH1 0x60 DUP7 ADD DUP3 PUSH2 0x73F JUMP JUMPDEST POP PUSH1 0x80 DUP4 ADD MLOAD PUSH2 0x7B9 PUSH1 0x80 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP DUP1 SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7D0 DUP4 DUP4 PUSH2 0x74E JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7F0 DUP3 PUSH2 0x643 JUMP JUMPDEST PUSH2 0x7FA DUP2 DUP6 PUSH2 0x64E JUMP JUMPDEST SWAP4 POP DUP4 PUSH1 0x20 DUP3 MUL DUP6 ADD PUSH2 0x80C DUP6 PUSH2 0x659 JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP6 DUP2 LT ISZERO PUSH2 0x848 JUMPI DUP5 DUP5 SUB DUP10 MSTORE DUP2 MLOAD PUSH2 0x829 DUP6 DUP3 PUSH2 0x7C4 JUMP JUMPDEST SWAP5 POP PUSH2 0x834 DUP4 PUSH2 0x7D8 JUMP JUMPDEST SWAP3 POP PUSH1 0x20 DUP11 ADD SWAP10 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x810 JUMP JUMPDEST POP DUP3 SWAP8 POP DUP8 SWAP6 POP POP POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x874 DUP2 DUP5 PUSH2 0x7E5 JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x88A DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP2 EQ PUSH2 0x895 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x8A7 DUP2 PUSH2 0x881 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x8C3 JUMPI PUSH2 0x8C2 PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x8D1 DUP5 DUP3 DUP6 ADD PUSH2 0x898 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0xA0 DUP4 ADD PUSH1 0x0 DUP4 ADD MLOAD DUP5 DUP3 SUB PUSH1 0x0 DUP7 ADD MSTORE PUSH2 0x8F7 DUP3 DUP3 PUSH2 0x6BA JUMP JUMPDEST SWAP2 POP POP PUSH1 0x20 DUP4 ADD MLOAD PUSH2 0x90C PUSH1 0x20 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP PUSH1 0x40 DUP4 ADD MLOAD PUSH2 0x91F PUSH1 0x40 DUP7 ADD DUP3 PUSH2 0x726 JUMP JUMPDEST POP PUSH1 0x60 DUP4 ADD MLOAD PUSH2 0x932 PUSH1 0x60 DUP7 ADD DUP3 PUSH2 0x73F JUMP JUMPDEST POP PUSH1 0x80 DUP4 ADD MLOAD PUSH2 0x945 PUSH1 0x80 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP DUP1 SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x96A DUP2 DUP5 PUSH2 0x8DA JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x9AA DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x9C5 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x9A1 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x9D4 DUP2 PUSH2 0x712 JUMP JUMPDEST DUP2 EQ PUSH2 0x9DF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0x9F1 DUP2 PUSH2 0x9CB JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xA0D JUMPI PUSH2 0xA0C PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0xA1B DUP5 DUP3 DUP6 ADD PUSH2 0x9E2 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0xA33 DUP2 PUSH2 0x881 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xA4F JUMPI PUSH2 0xA4E PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0xA5D DUP5 DUP3 DUP6 ADD PUSH2 0xA24 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x12 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0xAA0 DUP3 PUSH2 0x735 JUMP JUMPDEST SWAP2 POP PUSH2 0xAAB DUP4 PUSH2 0x735 JUMP JUMPDEST SWAP3 POP DUP3 PUSH2 0xABB JUMPI PUSH2 0xABA PUSH2 0xA66 JUMP JUMPDEST JUMPDEST DUP3 DUP3 DIV SWAP1 POP SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 MSTORE8 STATICCALL PUSH9 0xF506445419B3AFB491 PUSH29 0x441141FBF14A060B975503BB970B737476AA3264736F6C634300081C00 CALLER ", "sourceMap": "209:1761:0:-:0;;;;;;;;;;;;;;;;;;;"}, "deployedBytecode": {"functionDebugData": {"@formatPrice_153": {"entryPoint": 1457, "id": 153, "parameterSlots": 1, "returnSlots": 1}, "@getAllPrices_138": {"entryPoint": 137, "id": 138, "parameterSlots": 0, "returnSlots": 1}, "@getPrice_61": {"entryPoint": 685, "id": 61, "parameterSlots": 1, "returnSlots": 1}, "@getSymbol_193": {"entryPoint": 1018, "id": 193, "parameterSlots": 1, "returnSlots": 1}, "abi_decode_t_uint32": {"entryPoint": 2200, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_t_uint32_fromMemory": {"entryPoint": 2596, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_t_uint64_fromMemory": {"entryPoint": 2530, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint32": {"entryPoint": 2221, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint32_fromMemory": {"entryPoint": 2617, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_decode_tuple_t_uint64_fromMemory": {"entryPoint": 2551, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encodeUpdatedPos_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr": {"entryPoint": 1988, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack": {"entryPoint": 2021, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr": {"entryPoint": 1722, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr": {"entryPoint": 1870, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr_fromStack": {"entryPoint": 2266, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_t_uint256_to_t_uint256": {"entryPoint": 1855, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint32_to_t_uint32": {"entryPoint": 1795, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint32_to_t_uint32_fromStack": {"entryPoint": 2465, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_t_uint64_to_t_uint64": {"entryPoint": 1830, "id": null, "parameterSlots": 2, "returnSlots": 0}, "abi_encode_tuple_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__fromStack_reversed": {"entryPoint": 2138, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_struct$_Price_$31_memory_ptr__to_t_struct$_Price_$31_memory_ptr__fromStack_reversed": {"entryPoint": 2384, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed": {"entryPoint": 2480, "id": null, "parameterSlots": 2, "returnSlots": 1}, "allocate_unbounded": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 1}, "array_dataslot_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr": {"entryPoint": 1625, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr": {"entryPoint": 1603, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_length_t_string_memory_ptr": {"entryPoint": 1635, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_nextElement_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr": {"entryPoint": 2008, "id": null, "parameterSlots": 1, "returnSlots": 1}, "array_storeLengthForEncoding_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack": {"entryPoint": 1614, "id": null, "parameterSlots": 2, "returnSlots": 1}, "array_storeLengthForEncoding_t_string_memory_ptr": {"entryPoint": 1646, "id": null, "parameterSlots": 2, "returnSlots": 1}, "checked_div_t_uint256": {"entryPoint": 2709, "id": null, "parameterSlots": 2, "returnSlots": 1}, "cleanup_t_uint256": {"entryPoint": 1845, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint32": {"entryPoint": 1779, "id": null, "parameterSlots": 1, "returnSlots": 1}, "cleanup_t_uint64": {"entryPoint": 1810, "id": null, "parameterSlots": 1, "returnSlots": 1}, "copy_memory_to_memory_with_cleanup": {"entryPoint": 1663, "id": null, "parameterSlots": 3, "returnSlots": 0}, "panic_error_0x11": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x12": {"entryPoint": 2662, "id": null, "parameterSlots": 0, "returnSlots": 0}, "panic_error_0x32": {"entryPoint": 2418, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db": {"entryPoint": null, "id": null, "parameterSlots": 0, "returnSlots": 0}, "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b": {"entryPoint": 2172, "id": null, "parameterSlots": 0, "returnSlots": 0}, "round_up_to_mul_of_32": {"entryPoint": 1705, "id": null, "parameterSlots": 1, "returnSlots": 1}, "validator_revert_t_uint32": {"entryPoint": 2177, "id": null, "parameterSlots": 1, "returnSlots": 0}, "validator_revert_t_uint64": {"entryPoint": 2507, "id": null, "parameterSlots": 1, "returnSlots": 0}}, "generatedSources": [{"ast": {"nativeSrc": "0:9940:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "0:9940:1", "statements": [{"body": {"nativeSrc": "100:32:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "100:32:1", "statements": [{"nativeSrc": "111:14:1", "nodeType": "YulAssignment", "src": "111:14:1", "value": {"kind": "number", "nativeSrc": "121:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "121:4:1", "type": "", "value": "0x05"}, "variableNames": [{"name": "length", "nativeSrc": "111:6:1", "nodeType": "YulIdentifier", "src": "111:6:1"}]}]}, "name": "array_length_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "7:125:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "83:5:1", "nodeType": "YulTypedName", "src": "83:5:1", "type": ""}], "returnVariables": [{"name": "length", "nativeSrc": "93:6:1", "nodeType": "YulTypedName", "src": "93:6:1", "type": ""}], "src": "7:125:1"}, {"body": {"nativeSrc": "268:34:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "268:34:1", "statements": [{"nativeSrc": "278:18:1", "nodeType": "YulAssignment", "src": "278:18:1", "value": {"name": "pos", "nativeSrc": "293:3:1", "nodeType": "YulIdentifier", "src": "293:3:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "278:11:1", "nodeType": "YulIdentifier", "src": "278:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack", "nativeSrc": "138:164:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "240:3:1", "nodeType": "YulTypedName", "src": "240:3:1", "type": ""}, {"name": "length", "nativeSrc": "245:6:1", "nodeType": "YulTypedName", "src": "245:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "256:11:1", "nodeType": "YulTypedName", "src": "256:11:1", "type": ""}], "src": "138:164:1"}, {"body": {"nativeSrc": "399:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "399:28:1", "statements": [{"nativeSrc": "409:11:1", "nodeType": "YulAssignment", "src": "409:11:1", "value": {"name": "ptr", "nativeSrc": "417:3:1", "nodeType": "YulIdentifier", "src": "417:3:1"}, "variableNames": [{"name": "data", "nativeSrc": "409:4:1", "nodeType": "YulIdentifier", "src": "409:4:1"}]}]}, "name": "array_dataslot_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "308:119:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "386:3:1", "nodeType": "YulTypedName", "src": "386:3:1", "type": ""}], "returnVariables": [{"name": "data", "nativeSrc": "394:4:1", "nodeType": "YulTypedName", "src": "394:4:1", "type": ""}], "src": "308:119:1"}, {"body": {"nativeSrc": "492:40:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "492:40:1", "statements": [{"nativeSrc": "503:22:1", "nodeType": "YulAssignment", "src": "503:22:1", "value": {"arguments": [{"name": "value", "nativeSrc": "519:5:1", "nodeType": "YulIdentifier", "src": "519:5:1"}], "functionName": {"name": "mload", "nativeSrc": "513:5:1", "nodeType": "YulIdentifier", "src": "513:5:1"}, "nativeSrc": "513:12:1", "nodeType": "YulFunctionCall", "src": "513:12:1"}, "variableNames": [{"name": "length", "nativeSrc": "503:6:1", "nodeType": "YulIdentifier", "src": "503:6:1"}]}]}, "name": "array_length_t_string_memory_ptr", "nativeSrc": "433:99:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "475:5:1", "nodeType": "YulTypedName", "src": "475:5:1", "type": ""}], "returnVariables": [{"name": "length", "nativeSrc": "485:6:1", "nodeType": "YulTypedName", "src": "485:6:1", "type": ""}], "src": "433:99:1"}, {"body": {"nativeSrc": "624:73:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "624:73:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "641:3:1", "nodeType": "YulIdentifier", "src": "641:3:1"}, {"name": "length", "nativeSrc": "646:6:1", "nodeType": "YulIdentifier", "src": "646:6:1"}], "functionName": {"name": "mstore", "nativeSrc": "634:6:1", "nodeType": "YulIdentifier", "src": "634:6:1"}, "nativeSrc": "634:19:1", "nodeType": "YulFunctionCall", "src": "634:19:1"}, "nativeSrc": "634:19:1", "nodeType": "YulExpressionStatement", "src": "634:19:1"}, {"nativeSrc": "662:29:1", "nodeType": "YulAssignment", "src": "662:29:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "681:3:1", "nodeType": "YulIdentifier", "src": "681:3:1"}, {"kind": "number", "nativeSrc": "686:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "686:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "677:3:1", "nodeType": "YulIdentifier", "src": "677:3:1"}, "nativeSrc": "677:14:1", "nodeType": "YulFunctionCall", "src": "677:14:1"}, "variableNames": [{"name": "updated_pos", "nativeSrc": "662:11:1", "nodeType": "YulIdentifier", "src": "662:11:1"}]}]}, "name": "array_storeLengthForEncoding_t_string_memory_ptr", "nativeSrc": "538:159:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "pos", "nativeSrc": "596:3:1", "nodeType": "YulTypedName", "src": "596:3:1", "type": ""}, {"name": "length", "nativeSrc": "601:6:1", "nodeType": "YulTypedName", "src": "601:6:1", "type": ""}], "returnVariables": [{"name": "updated_pos", "nativeSrc": "612:11:1", "nodeType": "YulTypedName", "src": "612:11:1", "type": ""}], "src": "538:159:1"}, {"body": {"nativeSrc": "765:186:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "765:186:1", "statements": [{"nativeSrc": "776:10:1", "nodeType": "YulVariableDeclaration", "src": "776:10:1", "value": {"kind": "number", "nativeSrc": "785:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "785:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nativeSrc": "780:1:1", "nodeType": "YulTypedName", "src": "780:1:1", "type": ""}]}, {"body": {"nativeSrc": "845:63:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "845:63:1", "statements": [{"expression": {"arguments": [{"arguments": [{"name": "dst", "nativeSrc": "870:3:1", "nodeType": "YulIdentifier", "src": "870:3:1"}, {"name": "i", "nativeSrc": "875:1:1", "nodeType": "YulIdentifier", "src": "875:1:1"}], "functionName": {"name": "add", "nativeSrc": "866:3:1", "nodeType": "YulIdentifier", "src": "866:3:1"}, "nativeSrc": "866:11:1", "nodeType": "YulFunctionCall", "src": "866:11:1"}, {"arguments": [{"arguments": [{"name": "src", "nativeSrc": "889:3:1", "nodeType": "YulIdentifier", "src": "889:3:1"}, {"name": "i", "nativeSrc": "894:1:1", "nodeType": "YulIdentifier", "src": "894:1:1"}], "functionName": {"name": "add", "nativeSrc": "885:3:1", "nodeType": "YulIdentifier", "src": "885:3:1"}, "nativeSrc": "885:11:1", "nodeType": "YulFunctionCall", "src": "885:11:1"}], "functionName": {"name": "mload", "nativeSrc": "879:5:1", "nodeType": "YulIdentifier", "src": "879:5:1"}, "nativeSrc": "879:18:1", "nodeType": "YulFunctionCall", "src": "879:18:1"}], "functionName": {"name": "mstore", "nativeSrc": "859:6:1", "nodeType": "YulIdentifier", "src": "859:6:1"}, "nativeSrc": "859:39:1", "nodeType": "YulFunctionCall", "src": "859:39:1"}, "nativeSrc": "859:39:1", "nodeType": "YulExpressionStatement", "src": "859:39:1"}]}, "condition": {"arguments": [{"name": "i", "nativeSrc": "806:1:1", "nodeType": "YulIdentifier", "src": "806:1:1"}, {"name": "length", "nativeSrc": "809:6:1", "nodeType": "YulIdentifier", "src": "809:6:1"}], "functionName": {"name": "lt", "nativeSrc": "803:2:1", "nodeType": "YulIdentifier", "src": "803:2:1"}, "nativeSrc": "803:13:1", "nodeType": "YulFunctionCall", "src": "803:13:1"}, "nativeSrc": "795:113:1", "nodeType": "YulForLoop", "post": {"nativeSrc": "817:19:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "817:19:1", "statements": [{"nativeSrc": "819:15:1", "nodeType": "YulAssignment", "src": "819:15:1", "value": {"arguments": [{"name": "i", "nativeSrc": "828:1:1", "nodeType": "YulIdentifier", "src": "828:1:1"}, {"kind": "number", "nativeSrc": "831:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "831:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "824:3:1", "nodeType": "YulIdentifier", "src": "824:3:1"}, "nativeSrc": "824:10:1", "nodeType": "YulFunctionCall", "src": "824:10:1"}, "variableNames": [{"name": "i", "nativeSrc": "819:1:1", "nodeType": "YulIdentifier", "src": "819:1:1"}]}]}, "pre": {"nativeSrc": "799:3:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "799:3:1", "statements": []}, "src": "795:113:1"}, {"expression": {"arguments": [{"arguments": [{"name": "dst", "nativeSrc": "928:3:1", "nodeType": "YulIdentifier", "src": "928:3:1"}, {"name": "length", "nativeSrc": "933:6:1", "nodeType": "YulIdentifier", "src": "933:6:1"}], "functionName": {"name": "add", "nativeSrc": "924:3:1", "nodeType": "YulIdentifier", "src": "924:3:1"}, "nativeSrc": "924:16:1", "nodeType": "YulFunctionCall", "src": "924:16:1"}, {"kind": "number", "nativeSrc": "942:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "942:1:1", "type": "", "value": "0"}], "functionName": {"name": "mstore", "nativeSrc": "917:6:1", "nodeType": "YulIdentifier", "src": "917:6:1"}, "nativeSrc": "917:27:1", "nodeType": "YulFunctionCall", "src": "917:27:1"}, "nativeSrc": "917:27:1", "nodeType": "YulExpressionStatement", "src": "917:27:1"}]}, "name": "copy_memory_to_memory_with_cleanup", "nativeSrc": "703:248:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "src", "nativeSrc": "747:3:1", "nodeType": "YulTypedName", "src": "747:3:1", "type": ""}, {"name": "dst", "nativeSrc": "752:3:1", "nodeType": "YulTypedName", "src": "752:3:1", "type": ""}, {"name": "length", "nativeSrc": "757:6:1", "nodeType": "YulTypedName", "src": "757:6:1", "type": ""}], "src": "703:248:1"}, {"body": {"nativeSrc": "1005:54:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1005:54:1", "statements": [{"nativeSrc": "1015:38:1", "nodeType": "YulAssignment", "src": "1015:38:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "1033:5:1", "nodeType": "YulIdentifier", "src": "1033:5:1"}, {"kind": "number", "nativeSrc": "1040:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1040:2:1", "type": "", "value": "31"}], "functionName": {"name": "add", "nativeSrc": "1029:3:1", "nodeType": "YulIdentifier", "src": "1029:3:1"}, "nativeSrc": "1029:14:1", "nodeType": "YulFunctionCall", "src": "1029:14:1"}, {"arguments": [{"kind": "number", "nativeSrc": "1049:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1049:2:1", "type": "", "value": "31"}], "functionName": {"name": "not", "nativeSrc": "1045:3:1", "nodeType": "YulIdentifier", "src": "1045:3:1"}, "nativeSrc": "1045:7:1", "nodeType": "YulFunctionCall", "src": "1045:7:1"}], "functionName": {"name": "and", "nativeSrc": "1025:3:1", "nodeType": "YulIdentifier", "src": "1025:3:1"}, "nativeSrc": "1025:28:1", "nodeType": "YulFunctionCall", "src": "1025:28:1"}, "variableNames": [{"name": "result", "nativeSrc": "1015:6:1", "nodeType": "YulIdentifier", "src": "1015:6:1"}]}]}, "name": "round_up_to_mul_of_32", "nativeSrc": "957:102:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "988:5:1", "nodeType": "YulTypedName", "src": "988:5:1", "type": ""}], "returnVariables": [{"name": "result", "nativeSrc": "998:6:1", "nodeType": "YulTypedName", "src": "998:6:1", "type": ""}], "src": "957:102:1"}, {"body": {"nativeSrc": "1147:275:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1147:275:1", "statements": [{"nativeSrc": "1157:53:1", "nodeType": "YulVariableDeclaration", "src": "1157:53:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1204:5:1", "nodeType": "YulIdentifier", "src": "1204:5:1"}], "functionName": {"name": "array_length_t_string_memory_ptr", "nativeSrc": "1171:32:1", "nodeType": "YulIdentifier", "src": "1171:32:1"}, "nativeSrc": "1171:39:1", "nodeType": "YulFunctionCall", "src": "1171:39:1"}, "variables": [{"name": "length", "nativeSrc": "1161:6:1", "nodeType": "YulTypedName", "src": "1161:6:1", "type": ""}]}, {"nativeSrc": "1219:68:1", "nodeType": "YulAssignment", "src": "1219:68:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "1275:3:1", "nodeType": "YulIdentifier", "src": "1275:3:1"}, {"name": "length", "nativeSrc": "1280:6:1", "nodeType": "YulIdentifier", "src": "1280:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_string_memory_ptr", "nativeSrc": "1226:48:1", "nodeType": "YulIdentifier", "src": "1226:48:1"}, "nativeSrc": "1226:61:1", "nodeType": "YulFunctionCall", "src": "1226:61:1"}, "variableNames": [{"name": "pos", "nativeSrc": "1219:3:1", "nodeType": "YulIdentifier", "src": "1219:3:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "1335:5:1", "nodeType": "YulIdentifier", "src": "1335:5:1"}, {"kind": "number", "nativeSrc": "1342:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1342:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "1331:3:1", "nodeType": "YulIdentifier", "src": "1331:3:1"}, "nativeSrc": "1331:16:1", "nodeType": "YulFunctionCall", "src": "1331:16:1"}, {"name": "pos", "nativeSrc": "1349:3:1", "nodeType": "YulIdentifier", "src": "1349:3:1"}, {"name": "length", "nativeSrc": "1354:6:1", "nodeType": "YulIdentifier", "src": "1354:6:1"}], "functionName": {"name": "copy_memory_to_memory_with_cleanup", "nativeSrc": "1296:34:1", "nodeType": "YulIdentifier", "src": "1296:34:1"}, "nativeSrc": "1296:65:1", "nodeType": "YulFunctionCall", "src": "1296:65:1"}, "nativeSrc": "1296:65:1", "nodeType": "YulExpressionStatement", "src": "1296:65:1"}, {"nativeSrc": "1370:46:1", "nodeType": "YulAssignment", "src": "1370:46:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "1381:3:1", "nodeType": "YulIdentifier", "src": "1381:3:1"}, {"arguments": [{"name": "length", "nativeSrc": "1408:6:1", "nodeType": "YulIdentifier", "src": "1408:6:1"}], "functionName": {"name": "round_up_to_mul_of_32", "nativeSrc": "1386:21:1", "nodeType": "YulIdentifier", "src": "1386:21:1"}, "nativeSrc": "1386:29:1", "nodeType": "YulFunctionCall", "src": "1386:29:1"}], "functionName": {"name": "add", "nativeSrc": "1377:3:1", "nodeType": "YulIdentifier", "src": "1377:3:1"}, "nativeSrc": "1377:39:1", "nodeType": "YulFunctionCall", "src": "1377:39:1"}, "variableNames": [{"name": "end", "nativeSrc": "1370:3:1", "nodeType": "YulIdentifier", "src": "1370:3:1"}]}]}, "name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr", "nativeSrc": "1065:357:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1128:5:1", "nodeType": "YulTypedName", "src": "1128:5:1", "type": ""}, {"name": "pos", "nativeSrc": "1135:3:1", "nodeType": "YulTypedName", "src": "1135:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "1143:3:1", "nodeType": "YulTypedName", "src": "1143:3:1", "type": ""}], "src": "1065:357:1"}, {"body": {"nativeSrc": "1472:49:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1472:49:1", "statements": [{"nativeSrc": "1482:33:1", "nodeType": "YulAssignment", "src": "1482:33:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1497:5:1", "nodeType": "YulIdentifier", "src": "1497:5:1"}, {"kind": "number", "nativeSrc": "1504:10:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1504:10:1", "type": "", "value": "0xffffffff"}], "functionName": {"name": "and", "nativeSrc": "1493:3:1", "nodeType": "YulIdentifier", "src": "1493:3:1"}, "nativeSrc": "1493:22:1", "nodeType": "YulFunctionCall", "src": "1493:22:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "1482:7:1", "nodeType": "YulIdentifier", "src": "1482:7:1"}]}]}, "name": "cleanup_t_uint32", "nativeSrc": "1428:93:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1454:5:1", "nodeType": "YulTypedName", "src": "1454:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "1464:7:1", "nodeType": "YulTypedName", "src": "1464:7:1", "type": ""}], "src": "1428:93:1"}, {"body": {"nativeSrc": "1580:52:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1580:52:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "1597:3:1", "nodeType": "YulIdentifier", "src": "1597:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "1619:5:1", "nodeType": "YulIdentifier", "src": "1619:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "1602:16:1", "nodeType": "YulIdentifier", "src": "1602:16:1"}, "nativeSrc": "1602:23:1", "nodeType": "YulFunctionCall", "src": "1602:23:1"}], "functionName": {"name": "mstore", "nativeSrc": "1590:6:1", "nodeType": "YulIdentifier", "src": "1590:6:1"}, "nativeSrc": "1590:36:1", "nodeType": "YulFunctionCall", "src": "1590:36:1"}, "nativeSrc": "1590:36:1", "nodeType": "YulExpressionStatement", "src": "1590:36:1"}]}, "name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "1527:105:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1568:5:1", "nodeType": "YulTypedName", "src": "1568:5:1", "type": ""}, {"name": "pos", "nativeSrc": "1575:3:1", "nodeType": "YulTypedName", "src": "1575:3:1", "type": ""}], "src": "1527:105:1"}, {"body": {"nativeSrc": "1682:57:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1682:57:1", "statements": [{"nativeSrc": "1692:41:1", "nodeType": "YulAssignment", "src": "1692:41:1", "value": {"arguments": [{"name": "value", "nativeSrc": "1707:5:1", "nodeType": "YulIdentifier", "src": "1707:5:1"}, {"kind": "number", "nativeSrc": "1714:18:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "1714:18:1", "type": "", "value": "0xffffffffffffffff"}], "functionName": {"name": "and", "nativeSrc": "1703:3:1", "nodeType": "YulIdentifier", "src": "1703:3:1"}, "nativeSrc": "1703:30:1", "nodeType": "YulFunctionCall", "src": "1703:30:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "1692:7:1", "nodeType": "YulIdentifier", "src": "1692:7:1"}]}]}, "name": "cleanup_t_uint64", "nativeSrc": "1638:101:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1664:5:1", "nodeType": "YulTypedName", "src": "1664:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "1674:7:1", "nodeType": "YulTypedName", "src": "1674:7:1", "type": ""}], "src": "1638:101:1"}, {"body": {"nativeSrc": "1798:52:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1798:52:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "1815:3:1", "nodeType": "YulIdentifier", "src": "1815:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "1837:5:1", "nodeType": "YulIdentifier", "src": "1837:5:1"}], "functionName": {"name": "cleanup_t_uint64", "nativeSrc": "1820:16:1", "nodeType": "YulIdentifier", "src": "1820:16:1"}, "nativeSrc": "1820:23:1", "nodeType": "YulFunctionCall", "src": "1820:23:1"}], "functionName": {"name": "mstore", "nativeSrc": "1808:6:1", "nodeType": "YulIdentifier", "src": "1808:6:1"}, "nativeSrc": "1808:36:1", "nodeType": "YulFunctionCall", "src": "1808:36:1"}, "nativeSrc": "1808:36:1", "nodeType": "YulExpressionStatement", "src": "1808:36:1"}]}, "name": "abi_encode_t_uint64_to_t_uint64", "nativeSrc": "1745:105:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1786:5:1", "nodeType": "YulTypedName", "src": "1786:5:1", "type": ""}, {"name": "pos", "nativeSrc": "1793:3:1", "nodeType": "YulTypedName", "src": "1793:3:1", "type": ""}], "src": "1745:105:1"}, {"body": {"nativeSrc": "1901:32:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1901:32:1", "statements": [{"nativeSrc": "1911:16:1", "nodeType": "YulAssignment", "src": "1911:16:1", "value": {"name": "value", "nativeSrc": "1922:5:1", "nodeType": "YulIdentifier", "src": "1922:5:1"}, "variableNames": [{"name": "cleaned", "nativeSrc": "1911:7:1", "nodeType": "YulIdentifier", "src": "1911:7:1"}]}]}, "name": "cleanup_t_uint256", "nativeSrc": "1856:77:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1883:5:1", "nodeType": "YulTypedName", "src": "1883:5:1", "type": ""}], "returnVariables": [{"name": "cleaned", "nativeSrc": "1893:7:1", "nodeType": "YulTypedName", "src": "1893:7:1", "type": ""}], "src": "1856:77:1"}, {"body": {"nativeSrc": "1994:53:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "1994:53:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "2011:3:1", "nodeType": "YulIdentifier", "src": "2011:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "2034:5:1", "nodeType": "YulIdentifier", "src": "2034:5:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "2016:17:1", "nodeType": "YulIdentifier", "src": "2016:17:1"}, "nativeSrc": "2016:24:1", "nodeType": "YulFunctionCall", "src": "2016:24:1"}], "functionName": {"name": "mstore", "nativeSrc": "2004:6:1", "nodeType": "YulIdentifier", "src": "2004:6:1"}, "nativeSrc": "2004:37:1", "nodeType": "YulFunctionCall", "src": "2004:37:1"}, "nativeSrc": "2004:37:1", "nodeType": "YulExpressionStatement", "src": "2004:37:1"}]}, "name": "abi_encode_t_uint256_to_t_uint256", "nativeSrc": "1939:108:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "1982:5:1", "nodeType": "YulTypedName", "src": "1982:5:1", "type": ""}, {"name": "pos", "nativeSrc": "1989:3:1", "nodeType": "YulTypedName", "src": "1989:3:1", "type": ""}], "src": "1939:108:1"}, {"body": {"nativeSrc": "2217:1024:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2217:1024:1", "statements": [{"nativeSrc": "2227:26:1", "nodeType": "YulVariableDeclaration", "src": "2227:26:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "2243:3:1", "nodeType": "YulIdentifier", "src": "2243:3:1"}, {"kind": "number", "nativeSrc": "2248:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2248:4:1", "type": "", "value": "0xa0"}], "functionName": {"name": "add", "nativeSrc": "2239:3:1", "nodeType": "YulIdentifier", "src": "2239:3:1"}, "nativeSrc": "2239:14:1", "nodeType": "YulFunctionCall", "src": "2239:14:1"}, "variables": [{"name": "tail", "nativeSrc": "2231:4:1", "nodeType": "YulTypedName", "src": "2231:4:1", "type": ""}]}, {"nativeSrc": "2263:237:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2263:237:1", "statements": [{"nativeSrc": "2300:43:1", "nodeType": "YulVariableDeclaration", "src": "2300:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "2330:5:1", "nodeType": "YulIdentifier", "src": "2330:5:1"}, {"kind": "number", "nativeSrc": "2337:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2337:4:1", "type": "", "value": "0x00"}], "functionName": {"name": "add", "nativeSrc": "2326:3:1", "nodeType": "YulIdentifier", "src": "2326:3:1"}, "nativeSrc": "2326:16:1", "nodeType": "YulFunctionCall", "src": "2326:16:1"}], "functionName": {"name": "mload", "nativeSrc": "2320:5:1", "nodeType": "YulIdentifier", "src": "2320:5:1"}, "nativeSrc": "2320:23:1", "nodeType": "YulFunctionCall", "src": "2320:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "2304:12:1", "nodeType": "YulTypedName", "src": "2304:12:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "pos", "nativeSrc": "2368:3:1", "nodeType": "YulIdentifier", "src": "2368:3:1"}, {"kind": "number", "nativeSrc": "2373:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2373:4:1", "type": "", "value": "0x00"}], "functionName": {"name": "add", "nativeSrc": "2364:3:1", "nodeType": "YulIdentifier", "src": "2364:3:1"}, "nativeSrc": "2364:14:1", "nodeType": "YulFunctionCall", "src": "2364:14:1"}, {"arguments": [{"name": "tail", "nativeSrc": "2384:4:1", "nodeType": "YulIdentifier", "src": "2384:4:1"}, {"name": "pos", "nativeSrc": "2390:3:1", "nodeType": "YulIdentifier", "src": "2390:3:1"}], "functionName": {"name": "sub", "nativeSrc": "2380:3:1", "nodeType": "YulIdentifier", "src": "2380:3:1"}, "nativeSrc": "2380:14:1", "nodeType": "YulFunctionCall", "src": "2380:14:1"}], "functionName": {"name": "mstore", "nativeSrc": "2357:6:1", "nodeType": "YulIdentifier", "src": "2357:6:1"}, "nativeSrc": "2357:38:1", "nodeType": "YulFunctionCall", "src": "2357:38:1"}, "nativeSrc": "2357:38:1", "nodeType": "YulExpressionStatement", "src": "2357:38:1"}, {"nativeSrc": "2408:81:1", "nodeType": "YulAssignment", "src": "2408:81:1", "value": {"arguments": [{"name": "memberValue0", "nativeSrc": "2470:12:1", "nodeType": "YulIdentifier", "src": "2470:12:1"}, {"name": "tail", "nativeSrc": "2484:4:1", "nodeType": "YulIdentifier", "src": "2484:4:1"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr", "nativeSrc": "2416:53:1", "nodeType": "YulIdentifier", "src": "2416:53:1"}, "nativeSrc": "2416:73:1", "nodeType": "YulFunctionCall", "src": "2416:73:1"}, "variableNames": [{"name": "tail", "nativeSrc": "2408:4:1", "nodeType": "YulIdentifier", "src": "2408:4:1"}]}]}, {"nativeSrc": "2510:165:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2510:165:1", "statements": [{"nativeSrc": "2548:43:1", "nodeType": "YulVariableDeclaration", "src": "2548:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "2578:5:1", "nodeType": "YulIdentifier", "src": "2578:5:1"}, {"kind": "number", "nativeSrc": "2585:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2585:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2574:3:1", "nodeType": "YulIdentifier", "src": "2574:3:1"}, "nativeSrc": "2574:16:1", "nodeType": "YulFunctionCall", "src": "2574:16:1"}], "functionName": {"name": "mload", "nativeSrc": "2568:5:1", "nodeType": "YulIdentifier", "src": "2568:5:1"}, "nativeSrc": "2568:23:1", "nodeType": "YulFunctionCall", "src": "2568:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "2552:12:1", "nodeType": "YulTypedName", "src": "2552:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "2636:12:1", "nodeType": "YulIdentifier", "src": "2636:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "2654:3:1", "nodeType": "YulIdentifier", "src": "2654:3:1"}, {"kind": "number", "nativeSrc": "2659:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2659:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "2650:3:1", "nodeType": "YulIdentifier", "src": "2650:3:1"}, "nativeSrc": "2650:14:1", "nodeType": "YulFunctionCall", "src": "2650:14:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "2604:31:1", "nodeType": "YulIdentifier", "src": "2604:31:1"}, "nativeSrc": "2604:61:1", "nodeType": "YulFunctionCall", "src": "2604:61:1"}, "nativeSrc": "2604:61:1", "nodeType": "YulExpressionStatement", "src": "2604:61:1"}]}, {"nativeSrc": "2685:166:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2685:166:1", "statements": [{"nativeSrc": "2724:43:1", "nodeType": "YulVariableDeclaration", "src": "2724:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "2754:5:1", "nodeType": "YulIdentifier", "src": "2754:5:1"}, {"kind": "number", "nativeSrc": "2761:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2761:4:1", "type": "", "value": "0x40"}], "functionName": {"name": "add", "nativeSrc": "2750:3:1", "nodeType": "YulIdentifier", "src": "2750:3:1"}, "nativeSrc": "2750:16:1", "nodeType": "YulFunctionCall", "src": "2750:16:1"}], "functionName": {"name": "mload", "nativeSrc": "2744:5:1", "nodeType": "YulIdentifier", "src": "2744:5:1"}, "nativeSrc": "2744:23:1", "nodeType": "YulFunctionCall", "src": "2744:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "2728:12:1", "nodeType": "YulTypedName", "src": "2728:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "2812:12:1", "nodeType": "YulIdentifier", "src": "2812:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "2830:3:1", "nodeType": "YulIdentifier", "src": "2830:3:1"}, {"kind": "number", "nativeSrc": "2835:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2835:4:1", "type": "", "value": "0x40"}], "functionName": {"name": "add", "nativeSrc": "2826:3:1", "nodeType": "YulIdentifier", "src": "2826:3:1"}, "nativeSrc": "2826:14:1", "nodeType": "YulFunctionCall", "src": "2826:14:1"}], "functionName": {"name": "abi_encode_t_uint64_to_t_uint64", "nativeSrc": "2780:31:1", "nodeType": "YulIdentifier", "src": "2780:31:1"}, "nativeSrc": "2780:61:1", "nodeType": "YulFunctionCall", "src": "2780:61:1"}, "nativeSrc": "2780:61:1", "nodeType": "YulExpressionStatement", "src": "2780:61:1"}]}, {"nativeSrc": "2861:174:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "2861:174:1", "statements": [{"nativeSrc": "2906:43:1", "nodeType": "YulVariableDeclaration", "src": "2906:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "2936:5:1", "nodeType": "YulIdentifier", "src": "2936:5:1"}, {"kind": "number", "nativeSrc": "2943:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "2943:4:1", "type": "", "value": "0x60"}], "functionName": {"name": "add", "nativeSrc": "2932:3:1", "nodeType": "YulIdentifier", "src": "2932:3:1"}, "nativeSrc": "2932:16:1", "nodeType": "YulFunctionCall", "src": "2932:16:1"}], "functionName": {"name": "mload", "nativeSrc": "2926:5:1", "nodeType": "YulIdentifier", "src": "2926:5:1"}, "nativeSrc": "2926:23:1", "nodeType": "YulFunctionCall", "src": "2926:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "2910:12:1", "nodeType": "YulTypedName", "src": "2910:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "2996:12:1", "nodeType": "YulIdentifier", "src": "2996:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "3014:3:1", "nodeType": "YulIdentifier", "src": "3014:3:1"}, {"kind": "number", "nativeSrc": "3019:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3019:4:1", "type": "", "value": "0x60"}], "functionName": {"name": "add", "nativeSrc": "3010:3:1", "nodeType": "YulIdentifier", "src": "3010:3:1"}, "nativeSrc": "3010:14:1", "nodeType": "YulFunctionCall", "src": "3010:14:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256", "nativeSrc": "2962:33:1", "nodeType": "YulIdentifier", "src": "2962:33:1"}, "nativeSrc": "2962:63:1", "nodeType": "YulFunctionCall", "src": "2962:63:1"}, "nativeSrc": "2962:63:1", "nodeType": "YulExpressionStatement", "src": "2962:63:1"}]}, {"nativeSrc": "3045:169:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3045:169:1", "statements": [{"nativeSrc": "3087:43:1", "nodeType": "YulVariableDeclaration", "src": "3087:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "3117:5:1", "nodeType": "YulIdentifier", "src": "3117:5:1"}, {"kind": "number", "nativeSrc": "3124:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3124:4:1", "type": "", "value": "0x80"}], "functionName": {"name": "add", "nativeSrc": "3113:3:1", "nodeType": "YulIdentifier", "src": "3113:3:1"}, "nativeSrc": "3113:16:1", "nodeType": "YulFunctionCall", "src": "3113:16:1"}], "functionName": {"name": "mload", "nativeSrc": "3107:5:1", "nodeType": "YulIdentifier", "src": "3107:5:1"}, "nativeSrc": "3107:23:1", "nodeType": "YulFunctionCall", "src": "3107:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "3091:12:1", "nodeType": "YulTypedName", "src": "3091:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "3175:12:1", "nodeType": "YulIdentifier", "src": "3175:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "3193:3:1", "nodeType": "YulIdentifier", "src": "3193:3:1"}, {"kind": "number", "nativeSrc": "3198:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3198:4:1", "type": "", "value": "0x80"}], "functionName": {"name": "add", "nativeSrc": "3189:3:1", "nodeType": "YulIdentifier", "src": "3189:3:1"}, "nativeSrc": "3189:14:1", "nodeType": "YulFunctionCall", "src": "3189:14:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "3143:31:1", "nodeType": "YulIdentifier", "src": "3143:31:1"}, "nativeSrc": "3143:61:1", "nodeType": "YulFunctionCall", "src": "3143:61:1"}, "nativeSrc": "3143:61:1", "nodeType": "YulExpressionStatement", "src": "3143:61:1"}]}, {"nativeSrc": "3224:11:1", "nodeType": "YulAssignment", "src": "3224:11:1", "value": {"name": "tail", "nativeSrc": "3231:4:1", "nodeType": "YulIdentifier", "src": "3231:4:1"}, "variableNames": [{"name": "end", "nativeSrc": "3224:3:1", "nodeType": "YulIdentifier", "src": "3224:3:1"}]}]}, "name": "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr", "nativeSrc": "2111:1130:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "2196:5:1", "nodeType": "YulTypedName", "src": "2196:5:1", "type": ""}, {"name": "pos", "nativeSrc": "2203:3:1", "nodeType": "YulTypedName", "src": "2203:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "2212:3:1", "nodeType": "YulTypedName", "src": "2212:3:1", "type": ""}], "src": "2111:1130:1"}, {"body": {"nativeSrc": "3369:118:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3369:118:1", "statements": [{"nativeSrc": "3379:102:1", "nodeType": "YulAssignment", "src": "3379:102:1", "value": {"arguments": [{"name": "value0", "nativeSrc": "3469:6:1", "nodeType": "YulIdentifier", "src": "3469:6:1"}, {"name": "pos", "nativeSrc": "3477:3:1", "nodeType": "YulIdentifier", "src": "3477:3:1"}], "functionName": {"name": "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr", "nativeSrc": "3393:75:1", "nodeType": "YulIdentifier", "src": "3393:75:1"}, "nativeSrc": "3393:88:1", "nodeType": "YulFunctionCall", "src": "3393:88:1"}, "variableNames": [{"name": "updatedPos", "nativeSrc": "3379:10:1", "nodeType": "YulIdentifier", "src": "3379:10:1"}]}]}, "name": "abi_encodeUpdatedPos_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr", "nativeSrc": "3247:240:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value0", "nativeSrc": "3342:6:1", "nodeType": "YulTypedName", "src": "3342:6:1", "type": ""}, {"name": "pos", "nativeSrc": "3350:3:1", "nodeType": "YulTypedName", "src": "3350:3:1", "type": ""}], "returnVariables": [{"name": "updatedPos", "nativeSrc": "3358:10:1", "nodeType": "YulTypedName", "src": "3358:10:1", "type": ""}], "src": "3247:240:1"}, {"body": {"nativeSrc": "3587:38:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3587:38:1", "statements": [{"nativeSrc": "3597:22:1", "nodeType": "YulAssignment", "src": "3597:22:1", "value": {"arguments": [{"name": "ptr", "nativeSrc": "3609:3:1", "nodeType": "YulIdentifier", "src": "3609:3:1"}, {"kind": "number", "nativeSrc": "3614:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "3614:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "3605:3:1", "nodeType": "YulIdentifier", "src": "3605:3:1"}, "nativeSrc": "3605:14:1", "nodeType": "YulFunctionCall", "src": "3605:14:1"}, "variableNames": [{"name": "next", "nativeSrc": "3597:4:1", "nodeType": "YulIdentifier", "src": "3597:4:1"}]}]}, "name": "array_nextElement_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "3493:132:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "ptr", "nativeSrc": "3574:3:1", "nodeType": "YulTypedName", "src": "3574:3:1", "type": ""}], "returnVariables": [{"name": "next", "nativeSrc": "3582:4:1", "nodeType": "YulTypedName", "src": "3582:4:1", "type": ""}], "src": "3493:132:1"}, {"body": {"nativeSrc": "3857:905:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "3857:905:1", "statements": [{"nativeSrc": "3867:87:1", "nodeType": "YulVariableDeclaration", "src": "3867:87:1", "value": {"arguments": [{"name": "value", "nativeSrc": "3948:5:1", "nodeType": "YulIdentifier", "src": "3948:5:1"}], "functionName": {"name": "array_length_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "3881:66:1", "nodeType": "YulIdentifier", "src": "3881:66:1"}, "nativeSrc": "3881:73:1", "nodeType": "YulFunctionCall", "src": "3881:73:1"}, "variables": [{"name": "length", "nativeSrc": "3871:6:1", "nodeType": "YulTypedName", "src": "3871:6:1", "type": ""}]}, {"nativeSrc": "3963:112:1", "nodeType": "YulAssignment", "src": "3963:112:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "4063:3:1", "nodeType": "YulIdentifier", "src": "4063:3:1"}, {"name": "length", "nativeSrc": "4068:6:1", "nodeType": "YulIdentifier", "src": "4068:6:1"}], "functionName": {"name": "array_storeLengthForEncoding_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack", "nativeSrc": "3970:92:1", "nodeType": "YulIdentifier", "src": "3970:92:1"}, "nativeSrc": "3970:105:1", "nodeType": "YulFunctionCall", "src": "3970:105:1"}, "variableNames": [{"name": "pos", "nativeSrc": "3963:3:1", "nodeType": "YulIdentifier", "src": "3963:3:1"}]}, {"nativeSrc": "4084:20:1", "nodeType": "YulVariableDeclaration", "src": "4084:20:1", "value": {"name": "pos", "nativeSrc": "4101:3:1", "nodeType": "YulIdentifier", "src": "4101:3:1"}, "variables": [{"name": "headStart", "nativeSrc": "4088:9:1", "nodeType": "YulTypedName", "src": "4088:9:1", "type": ""}]}, {"nativeSrc": "4113:39:1", "nodeType": "YulVariableDeclaration", "src": "4113:39:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "4129:3:1", "nodeType": "YulIdentifier", "src": "4129:3:1"}, {"arguments": [{"name": "length", "nativeSrc": "4138:6:1", "nodeType": "YulIdentifier", "src": "4138:6:1"}, {"kind": "number", "nativeSrc": "4146:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4146:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "mul", "nativeSrc": "4134:3:1", "nodeType": "YulIdentifier", "src": "4134:3:1"}, "nativeSrc": "4134:17:1", "nodeType": "YulFunctionCall", "src": "4134:17:1"}], "functionName": {"name": "add", "nativeSrc": "4125:3:1", "nodeType": "YulIdentifier", "src": "4125:3:1"}, "nativeSrc": "4125:27:1", "nodeType": "YulFunctionCall", "src": "4125:27:1"}, "variables": [{"name": "tail", "nativeSrc": "4117:4:1", "nodeType": "YulTypedName", "src": "4117:4:1", "type": ""}]}, {"nativeSrc": "4161:90:1", "nodeType": "YulVariableDeclaration", "src": "4161:90:1", "value": {"arguments": [{"name": "value", "nativeSrc": "4245:5:1", "nodeType": "YulIdentifier", "src": "4245:5:1"}], "functionName": {"name": "array_dataslot_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "4176:68:1", "nodeType": "YulIdentifier", "src": "4176:68:1"}, "nativeSrc": "4176:75:1", "nodeType": "YulFunctionCall", "src": "4176:75:1"}, "variables": [{"name": "baseRef", "nativeSrc": "4165:7:1", "nodeType": "YulTypedName", "src": "4165:7:1", "type": ""}]}, {"nativeSrc": "4260:21:1", "nodeType": "YulVariableDeclaration", "src": "4260:21:1", "value": {"name": "baseRef", "nativeSrc": "4274:7:1", "nodeType": "YulIdentifier", "src": "4274:7:1"}, "variables": [{"name": "srcPtr", "nativeSrc": "4264:6:1", "nodeType": "YulTypedName", "src": "4264:6:1", "type": ""}]}, {"body": {"nativeSrc": "4350:367:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4350:367:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "4371:3:1", "nodeType": "YulIdentifier", "src": "4371:3:1"}, {"arguments": [{"name": "tail", "nativeSrc": "4380:4:1", "nodeType": "YulIdentifier", "src": "4380:4:1"}, {"name": "headStart", "nativeSrc": "4386:9:1", "nodeType": "YulIdentifier", "src": "4386:9:1"}], "functionName": {"name": "sub", "nativeSrc": "4376:3:1", "nodeType": "YulIdentifier", "src": "4376:3:1"}, "nativeSrc": "4376:20:1", "nodeType": "YulFunctionCall", "src": "4376:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "4364:6:1", "nodeType": "YulIdentifier", "src": "4364:6:1"}, "nativeSrc": "4364:33:1", "nodeType": "YulFunctionCall", "src": "4364:33:1"}, "nativeSrc": "4364:33:1", "nodeType": "YulExpressionStatement", "src": "4364:33:1"}, {"nativeSrc": "4410:34:1", "nodeType": "YulVariableDeclaration", "src": "4410:34:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "4437:6:1", "nodeType": "YulIdentifier", "src": "4437:6:1"}], "functionName": {"name": "mload", "nativeSrc": "4431:5:1", "nodeType": "YulIdentifier", "src": "4431:5:1"}, "nativeSrc": "4431:13:1", "nodeType": "YulFunctionCall", "src": "4431:13:1"}, "variables": [{"name": "elementValue0", "nativeSrc": "4414:13:1", "nodeType": "YulTypedName", "src": "4414:13:1", "type": ""}]}, {"nativeSrc": "4457:114:1", "nodeType": "YulAssignment", "src": "4457:114:1", "value": {"arguments": [{"name": "elementValue0", "nativeSrc": "4551:13:1", "nodeType": "YulIdentifier", "src": "4551:13:1"}, {"name": "tail", "nativeSrc": "4566:4:1", "nodeType": "YulIdentifier", "src": "4566:4:1"}], "functionName": {"name": "abi_encodeUpdatedPos_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr", "nativeSrc": "4465:85:1", "nodeType": "YulIdentifier", "src": "4465:85:1"}, "nativeSrc": "4465:106:1", "nodeType": "YulFunctionCall", "src": "4465:106:1"}, "variableNames": [{"name": "tail", "nativeSrc": "4457:4:1", "nodeType": "YulIdentifier", "src": "4457:4:1"}]}, {"nativeSrc": "4584:89:1", "nodeType": "YulAssignment", "src": "4584:89:1", "value": {"arguments": [{"name": "srcPtr", "nativeSrc": "4666:6:1", "nodeType": "YulIdentifier", "src": "4666:6:1"}], "functionName": {"name": "array_nextElement_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr", "nativeSrc": "4594:71:1", "nodeType": "YulIdentifier", "src": "4594:71:1"}, "nativeSrc": "4594:79:1", "nodeType": "YulFunctionCall", "src": "4594:79:1"}, "variableNames": [{"name": "srcPtr", "nativeSrc": "4584:6:1", "nodeType": "YulIdentifier", "src": "4584:6:1"}]}, {"nativeSrc": "4686:21:1", "nodeType": "YulAssignment", "src": "4686:21:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "4697:3:1", "nodeType": "YulIdentifier", "src": "4697:3:1"}, {"kind": "number", "nativeSrc": "4702:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4702:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "4693:3:1", "nodeType": "YulIdentifier", "src": "4693:3:1"}, "nativeSrc": "4693:14:1", "nodeType": "YulFunctionCall", "src": "4693:14:1"}, "variableNames": [{"name": "pos", "nativeSrc": "4686:3:1", "nodeType": "YulIdentifier", "src": "4686:3:1"}]}]}, "condition": {"arguments": [{"name": "i", "nativeSrc": "4312:1:1", "nodeType": "YulIdentifier", "src": "4312:1:1"}, {"name": "length", "nativeSrc": "4315:6:1", "nodeType": "YulIdentifier", "src": "4315:6:1"}], "functionName": {"name": "lt", "nativeSrc": "4309:2:1", "nodeType": "YulIdentifier", "src": "4309:2:1"}, "nativeSrc": "4309:13:1", "nodeType": "YulFunctionCall", "src": "4309:13:1"}, "nativeSrc": "4290:427:1", "nodeType": "YulForLoop", "post": {"nativeSrc": "4323:18:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4323:18:1", "statements": [{"nativeSrc": "4325:14:1", "nodeType": "YulAssignment", "src": "4325:14:1", "value": {"arguments": [{"name": "i", "nativeSrc": "4334:1:1", "nodeType": "YulIdentifier", "src": "4334:1:1"}, {"kind": "number", "nativeSrc": "4337:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4337:1:1", "type": "", "value": "1"}], "functionName": {"name": "add", "nativeSrc": "4330:3:1", "nodeType": "YulIdentifier", "src": "4330:3:1"}, "nativeSrc": "4330:9:1", "nodeType": "YulFunctionCall", "src": "4330:9:1"}, "variableNames": [{"name": "i", "nativeSrc": "4325:1:1", "nodeType": "YulIdentifier", "src": "4325:1:1"}]}]}, "pre": {"nativeSrc": "4294:14:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4294:14:1", "statements": [{"nativeSrc": "4296:10:1", "nodeType": "YulVariableDeclaration", "src": "4296:10:1", "value": {"kind": "number", "nativeSrc": "4305:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4305:1:1", "type": "", "value": "0"}, "variables": [{"name": "i", "nativeSrc": "4300:1:1", "nodeType": "YulTypedName", "src": "4300:1:1", "type": ""}]}]}, "src": "4290:427:1"}, {"nativeSrc": "4726:11:1", "nodeType": "YulAssignment", "src": "4726:11:1", "value": {"name": "tail", "nativeSrc": "4733:4:1", "nodeType": "YulIdentifier", "src": "4733:4:1"}, "variableNames": [{"name": "pos", "nativeSrc": "4726:3:1", "nodeType": "YulIdentifier", "src": "4726:3:1"}]}, {"nativeSrc": "4746:10:1", "nodeType": "YulAssignment", "src": "4746:10:1", "value": {"name": "pos", "nativeSrc": "4753:3:1", "nodeType": "YulIdentifier", "src": "4753:3:1"}, "variableNames": [{"name": "end", "nativeSrc": "4746:3:1", "nodeType": "YulIdentifier", "src": "4746:3:1"}]}]}, "name": "abi_encode_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack", "nativeSrc": "3695:1067:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "3836:5:1", "nodeType": "YulTypedName", "src": "3836:5:1", "type": ""}, {"name": "pos", "nativeSrc": "3843:3:1", "nodeType": "YulTypedName", "src": "3843:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "3852:3:1", "nodeType": "YulTypedName", "src": "3852:3:1", "type": ""}], "src": "3695:1067:1"}, {"body": {"nativeSrc": "4954:263:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "4954:263:1", "statements": [{"nativeSrc": "4964:26:1", "nodeType": "YulAssignment", "src": "4964:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "4976:9:1", "nodeType": "YulIdentifier", "src": "4976:9:1"}, {"kind": "number", "nativeSrc": "4987:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "4987:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "4972:3:1", "nodeType": "YulIdentifier", "src": "4972:3:1"}, "nativeSrc": "4972:18:1", "nodeType": "YulFunctionCall", "src": "4972:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "4964:4:1", "nodeType": "YulIdentifier", "src": "4964:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "5011:9:1", "nodeType": "YulIdentifier", "src": "5011:9:1"}, {"kind": "number", "nativeSrc": "5022:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5022:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "5007:3:1", "nodeType": "YulIdentifier", "src": "5007:3:1"}, "nativeSrc": "5007:17:1", "nodeType": "YulFunctionCall", "src": "5007:17:1"}, {"arguments": [{"name": "tail", "nativeSrc": "5030:4:1", "nodeType": "YulIdentifier", "src": "5030:4:1"}, {"name": "headStart", "nativeSrc": "5036:9:1", "nodeType": "YulIdentifier", "src": "5036:9:1"}], "functionName": {"name": "sub", "nativeSrc": "5026:3:1", "nodeType": "YulIdentifier", "src": "5026:3:1"}, "nativeSrc": "5026:20:1", "nodeType": "YulFunctionCall", "src": "5026:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "5000:6:1", "nodeType": "YulIdentifier", "src": "5000:6:1"}, "nativeSrc": "5000:47:1", "nodeType": "YulFunctionCall", "src": "5000:47:1"}, "nativeSrc": "5000:47:1", "nodeType": "YulExpressionStatement", "src": "5000:47:1"}, {"nativeSrc": "5056:154:1", "nodeType": "YulAssignment", "src": "5056:154:1", "value": {"arguments": [{"name": "value0", "nativeSrc": "5196:6:1", "nodeType": "YulIdentifier", "src": "5196:6:1"}, {"name": "tail", "nativeSrc": "5205:4:1", "nodeType": "YulIdentifier", "src": "5205:4:1"}], "functionName": {"name": "abi_encode_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack", "nativeSrc": "5064:131:1", "nodeType": "YulIdentifier", "src": "5064:131:1"}, "nativeSrc": "5064:146:1", "nodeType": "YulFunctionCall", "src": "5064:146:1"}, "variableNames": [{"name": "tail", "nativeSrc": "5056:4:1", "nodeType": "YulIdentifier", "src": "5056:4:1"}]}]}, "name": "abi_encode_tuple_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__fromStack_reversed", "nativeSrc": "4768:449:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "4926:9:1", "nodeType": "YulTypedName", "src": "4926:9:1", "type": ""}, {"name": "value0", "nativeSrc": "4938:6:1", "nodeType": "YulTypedName", "src": "4938:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "4949:4:1", "nodeType": "YulTypedName", "src": "4949:4:1", "type": ""}], "src": "4768:449:1"}, {"body": {"nativeSrc": "5263:35:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5263:35:1", "statements": [{"nativeSrc": "5273:19:1", "nodeType": "YulAssignment", "src": "5273:19:1", "value": {"arguments": [{"kind": "number", "nativeSrc": "5289:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5289:2:1", "type": "", "value": "64"}], "functionName": {"name": "mload", "nativeSrc": "5283:5:1", "nodeType": "YulIdentifier", "src": "5283:5:1"}, "nativeSrc": "5283:9:1", "nodeType": "YulFunctionCall", "src": "5283:9:1"}, "variableNames": [{"name": "memPtr", "nativeSrc": "5273:6:1", "nodeType": "YulIdentifier", "src": "5273:6:1"}]}]}, "name": "allocate_unbounded", "nativeSrc": "5223:75:1", "nodeType": "YulFunctionDefinition", "returnVariables": [{"name": "memPtr", "nativeSrc": "5256:6:1", "nodeType": "YulTypedName", "src": "5256:6:1", "type": ""}], "src": "5223:75:1"}, {"body": {"nativeSrc": "5393:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5393:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "5410:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5410:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "5413:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5413:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "5403:6:1", "nodeType": "YulIdentifier", "src": "5403:6:1"}, "nativeSrc": "5403:12:1", "nodeType": "YulFunctionCall", "src": "5403:12:1"}, "nativeSrc": "5403:12:1", "nodeType": "YulExpressionStatement", "src": "5403:12:1"}]}, "name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "5304:117:1", "nodeType": "YulFunctionDefinition", "src": "5304:117:1"}, {"body": {"nativeSrc": "5516:28:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5516:28:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "5533:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5533:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "5536:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5536:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "5526:6:1", "nodeType": "YulIdentifier", "src": "5526:6:1"}, "nativeSrc": "5526:12:1", "nodeType": "YulFunctionCall", "src": "5526:12:1"}, "nativeSrc": "5526:12:1", "nodeType": "YulExpressionStatement", "src": "5526:12:1"}]}, "name": "revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db", "nativeSrc": "5427:117:1", "nodeType": "YulFunctionDefinition", "src": "5427:117:1"}, {"body": {"nativeSrc": "5592:78:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5592:78:1", "statements": [{"body": {"nativeSrc": "5648:16:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5648:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "5657:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5657:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "5660:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5660:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "5650:6:1", "nodeType": "YulIdentifier", "src": "5650:6:1"}, "nativeSrc": "5650:12:1", "nodeType": "YulFunctionCall", "src": "5650:12:1"}, "nativeSrc": "5650:12:1", "nodeType": "YulExpressionStatement", "src": "5650:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "5615:5:1", "nodeType": "YulIdentifier", "src": "5615:5:1"}, {"arguments": [{"name": "value", "nativeSrc": "5639:5:1", "nodeType": "YulIdentifier", "src": "5639:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "5622:16:1", "nodeType": "YulIdentifier", "src": "5622:16:1"}, "nativeSrc": "5622:23:1", "nodeType": "YulFunctionCall", "src": "5622:23:1"}], "functionName": {"name": "eq", "nativeSrc": "5612:2:1", "nodeType": "YulIdentifier", "src": "5612:2:1"}, "nativeSrc": "5612:34:1", "nodeType": "YulFunctionCall", "src": "5612:34:1"}], "functionName": {"name": "iszero", "nativeSrc": "5605:6:1", "nodeType": "YulIdentifier", "src": "5605:6:1"}, "nativeSrc": "5605:42:1", "nodeType": "YulFunctionCall", "src": "5605:42:1"}, "nativeSrc": "5602:62:1", "nodeType": "YulIf", "src": "5602:62:1"}]}, "name": "validator_revert_t_uint32", "nativeSrc": "5550:120:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "5585:5:1", "nodeType": "YulTypedName", "src": "5585:5:1", "type": ""}], "src": "5550:120:1"}, {"body": {"nativeSrc": "5727:86:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5727:86:1", "statements": [{"nativeSrc": "5737:29:1", "nodeType": "YulAssignment", "src": "5737:29:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "5759:6:1", "nodeType": "YulIdentifier", "src": "5759:6:1"}], "functionName": {"name": "calldataload", "nativeSrc": "5746:12:1", "nodeType": "YulIdentifier", "src": "5746:12:1"}, "nativeSrc": "5746:20:1", "nodeType": "YulFunctionCall", "src": "5746:20:1"}, "variableNames": [{"name": "value", "nativeSrc": "5737:5:1", "nodeType": "YulIdentifier", "src": "5737:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "5801:5:1", "nodeType": "YulIdentifier", "src": "5801:5:1"}], "functionName": {"name": "validator_revert_t_uint32", "nativeSrc": "5775:25:1", "nodeType": "YulIdentifier", "src": "5775:25:1"}, "nativeSrc": "5775:32:1", "nodeType": "YulFunctionCall", "src": "5775:32:1"}, "nativeSrc": "5775:32:1", "nodeType": "YulExpressionStatement", "src": "5775:32:1"}]}, "name": "abi_decode_t_uint32", "nativeSrc": "5676:137:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "5705:6:1", "nodeType": "YulTypedName", "src": "5705:6:1", "type": ""}, {"name": "end", "nativeSrc": "5713:3:1", "nodeType": "YulTypedName", "src": "5713:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "5721:5:1", "nodeType": "YulTypedName", "src": "5721:5:1", "type": ""}], "src": "5676:137:1"}, {"body": {"nativeSrc": "5884:262:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5884:262:1", "statements": [{"body": {"nativeSrc": "5930:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "5930:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "5932:77:1", "nodeType": "YulIdentifier", "src": "5932:77:1"}, "nativeSrc": "5932:79:1", "nodeType": "YulFunctionCall", "src": "5932:79:1"}, "nativeSrc": "5932:79:1", "nodeType": "YulExpressionStatement", "src": "5932:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "5905:7:1", "nodeType": "YulIdentifier", "src": "5905:7:1"}, {"name": "headStart", "nativeSrc": "5914:9:1", "nodeType": "YulIdentifier", "src": "5914:9:1"}], "functionName": {"name": "sub", "nativeSrc": "5901:3:1", "nodeType": "YulIdentifier", "src": "5901:3:1"}, "nativeSrc": "5901:23:1", "nodeType": "YulFunctionCall", "src": "5901:23:1"}, {"kind": "number", "nativeSrc": "5926:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "5926:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "5897:3:1", "nodeType": "YulIdentifier", "src": "5897:3:1"}, "nativeSrc": "5897:32:1", "nodeType": "YulFunctionCall", "src": "5897:32:1"}, "nativeSrc": "5894:119:1", "nodeType": "YulIf", "src": "5894:119:1"}, {"nativeSrc": "6023:116:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6023:116:1", "statements": [{"nativeSrc": "6038:15:1", "nodeType": "YulVariableDeclaration", "src": "6038:15:1", "value": {"kind": "number", "nativeSrc": "6052:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6052:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "6042:6:1", "nodeType": "YulTypedName", "src": "6042:6:1", "type": ""}]}, {"nativeSrc": "6067:62:1", "nodeType": "YulAssignment", "src": "6067:62:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "6101:9:1", "nodeType": "YulIdentifier", "src": "6101:9:1"}, {"name": "offset", "nativeSrc": "6112:6:1", "nodeType": "YulIdentifier", "src": "6112:6:1"}], "functionName": {"name": "add", "nativeSrc": "6097:3:1", "nodeType": "YulIdentifier", "src": "6097:3:1"}, "nativeSrc": "6097:22:1", "nodeType": "YulFunctionCall", "src": "6097:22:1"}, {"name": "dataEnd", "nativeSrc": "6121:7:1", "nodeType": "YulIdentifier", "src": "6121:7:1"}], "functionName": {"name": "abi_decode_t_uint32", "nativeSrc": "6077:19:1", "nodeType": "YulIdentifier", "src": "6077:19:1"}, "nativeSrc": "6077:52:1", "nodeType": "YulFunctionCall", "src": "6077:52:1"}, "variableNames": [{"name": "value0", "nativeSrc": "6067:6:1", "nodeType": "YulIdentifier", "src": "6067:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint32", "nativeSrc": "5819:327:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "5854:9:1", "nodeType": "YulTypedName", "src": "5854:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "5865:7:1", "nodeType": "YulTypedName", "src": "5865:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "5877:6:1", "nodeType": "YulTypedName", "src": "5877:6:1", "type": ""}], "src": "5819:327:1"}, {"body": {"nativeSrc": "6326:1024:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6326:1024:1", "statements": [{"nativeSrc": "6336:26:1", "nodeType": "YulVariableDeclaration", "src": "6336:26:1", "value": {"arguments": [{"name": "pos", "nativeSrc": "6352:3:1", "nodeType": "YulIdentifier", "src": "6352:3:1"}, {"kind": "number", "nativeSrc": "6357:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6357:4:1", "type": "", "value": "0xa0"}], "functionName": {"name": "add", "nativeSrc": "6348:3:1", "nodeType": "YulIdentifier", "src": "6348:3:1"}, "nativeSrc": "6348:14:1", "nodeType": "YulFunctionCall", "src": "6348:14:1"}, "variables": [{"name": "tail", "nativeSrc": "6340:4:1", "nodeType": "YulTypedName", "src": "6340:4:1", "type": ""}]}, {"nativeSrc": "6372:237:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6372:237:1", "statements": [{"nativeSrc": "6409:43:1", "nodeType": "YulVariableDeclaration", "src": "6409:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "6439:5:1", "nodeType": "YulIdentifier", "src": "6439:5:1"}, {"kind": "number", "nativeSrc": "6446:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6446:4:1", "type": "", "value": "0x00"}], "functionName": {"name": "add", "nativeSrc": "6435:3:1", "nodeType": "YulIdentifier", "src": "6435:3:1"}, "nativeSrc": "6435:16:1", "nodeType": "YulFunctionCall", "src": "6435:16:1"}], "functionName": {"name": "mload", "nativeSrc": "6429:5:1", "nodeType": "YulIdentifier", "src": "6429:5:1"}, "nativeSrc": "6429:23:1", "nodeType": "YulFunctionCall", "src": "6429:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "6413:12:1", "nodeType": "YulTypedName", "src": "6413:12:1", "type": ""}]}, {"expression": {"arguments": [{"arguments": [{"name": "pos", "nativeSrc": "6477:3:1", "nodeType": "YulIdentifier", "src": "6477:3:1"}, {"kind": "number", "nativeSrc": "6482:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6482:4:1", "type": "", "value": "0x00"}], "functionName": {"name": "add", "nativeSrc": "6473:3:1", "nodeType": "YulIdentifier", "src": "6473:3:1"}, "nativeSrc": "6473:14:1", "nodeType": "YulFunctionCall", "src": "6473:14:1"}, {"arguments": [{"name": "tail", "nativeSrc": "6493:4:1", "nodeType": "YulIdentifier", "src": "6493:4:1"}, {"name": "pos", "nativeSrc": "6499:3:1", "nodeType": "YulIdentifier", "src": "6499:3:1"}], "functionName": {"name": "sub", "nativeSrc": "6489:3:1", "nodeType": "YulIdentifier", "src": "6489:3:1"}, "nativeSrc": "6489:14:1", "nodeType": "YulFunctionCall", "src": "6489:14:1"}], "functionName": {"name": "mstore", "nativeSrc": "6466:6:1", "nodeType": "YulIdentifier", "src": "6466:6:1"}, "nativeSrc": "6466:38:1", "nodeType": "YulFunctionCall", "src": "6466:38:1"}, "nativeSrc": "6466:38:1", "nodeType": "YulExpressionStatement", "src": "6466:38:1"}, {"nativeSrc": "6517:81:1", "nodeType": "YulAssignment", "src": "6517:81:1", "value": {"arguments": [{"name": "memberValue0", "nativeSrc": "6579:12:1", "nodeType": "YulIdentifier", "src": "6579:12:1"}, {"name": "tail", "nativeSrc": "6593:4:1", "nodeType": "YulIdentifier", "src": "6593:4:1"}], "functionName": {"name": "abi_encode_t_string_memory_ptr_to_t_string_memory_ptr", "nativeSrc": "6525:53:1", "nodeType": "YulIdentifier", "src": "6525:53:1"}, "nativeSrc": "6525:73:1", "nodeType": "YulFunctionCall", "src": "6525:73:1"}, "variableNames": [{"name": "tail", "nativeSrc": "6517:4:1", "nodeType": "YulIdentifier", "src": "6517:4:1"}]}]}, {"nativeSrc": "6619:165:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6619:165:1", "statements": [{"nativeSrc": "6657:43:1", "nodeType": "YulVariableDeclaration", "src": "6657:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "6687:5:1", "nodeType": "YulIdentifier", "src": "6687:5:1"}, {"kind": "number", "nativeSrc": "6694:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6694:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "6683:3:1", "nodeType": "YulIdentifier", "src": "6683:3:1"}, "nativeSrc": "6683:16:1", "nodeType": "YulFunctionCall", "src": "6683:16:1"}], "functionName": {"name": "mload", "nativeSrc": "6677:5:1", "nodeType": "YulIdentifier", "src": "6677:5:1"}, "nativeSrc": "6677:23:1", "nodeType": "YulFunctionCall", "src": "6677:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "6661:12:1", "nodeType": "YulTypedName", "src": "6661:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "6745:12:1", "nodeType": "YulIdentifier", "src": "6745:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "6763:3:1", "nodeType": "YulIdentifier", "src": "6763:3:1"}, {"kind": "number", "nativeSrc": "6768:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6768:4:1", "type": "", "value": "0x20"}], "functionName": {"name": "add", "nativeSrc": "6759:3:1", "nodeType": "YulIdentifier", "src": "6759:3:1"}, "nativeSrc": "6759:14:1", "nodeType": "YulFunctionCall", "src": "6759:14:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "6713:31:1", "nodeType": "YulIdentifier", "src": "6713:31:1"}, "nativeSrc": "6713:61:1", "nodeType": "YulFunctionCall", "src": "6713:61:1"}, "nativeSrc": "6713:61:1", "nodeType": "YulExpressionStatement", "src": "6713:61:1"}]}, {"nativeSrc": "6794:166:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6794:166:1", "statements": [{"nativeSrc": "6833:43:1", "nodeType": "YulVariableDeclaration", "src": "6833:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "6863:5:1", "nodeType": "YulIdentifier", "src": "6863:5:1"}, {"kind": "number", "nativeSrc": "6870:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6870:4:1", "type": "", "value": "0x40"}], "functionName": {"name": "add", "nativeSrc": "6859:3:1", "nodeType": "YulIdentifier", "src": "6859:3:1"}, "nativeSrc": "6859:16:1", "nodeType": "YulFunctionCall", "src": "6859:16:1"}], "functionName": {"name": "mload", "nativeSrc": "6853:5:1", "nodeType": "YulIdentifier", "src": "6853:5:1"}, "nativeSrc": "6853:23:1", "nodeType": "YulFunctionCall", "src": "6853:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "6837:12:1", "nodeType": "YulTypedName", "src": "6837:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "6921:12:1", "nodeType": "YulIdentifier", "src": "6921:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "6939:3:1", "nodeType": "YulIdentifier", "src": "6939:3:1"}, {"kind": "number", "nativeSrc": "6944:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "6944:4:1", "type": "", "value": "0x40"}], "functionName": {"name": "add", "nativeSrc": "6935:3:1", "nodeType": "YulIdentifier", "src": "6935:3:1"}, "nativeSrc": "6935:14:1", "nodeType": "YulFunctionCall", "src": "6935:14:1"}], "functionName": {"name": "abi_encode_t_uint64_to_t_uint64", "nativeSrc": "6889:31:1", "nodeType": "YulIdentifier", "src": "6889:31:1"}, "nativeSrc": "6889:61:1", "nodeType": "YulFunctionCall", "src": "6889:61:1"}, "nativeSrc": "6889:61:1", "nodeType": "YulExpressionStatement", "src": "6889:61:1"}]}, {"nativeSrc": "6970:174:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "6970:174:1", "statements": [{"nativeSrc": "7015:43:1", "nodeType": "YulVariableDeclaration", "src": "7015:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "7045:5:1", "nodeType": "YulIdentifier", "src": "7045:5:1"}, {"kind": "number", "nativeSrc": "7052:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7052:4:1", "type": "", "value": "0x60"}], "functionName": {"name": "add", "nativeSrc": "7041:3:1", "nodeType": "YulIdentifier", "src": "7041:3:1"}, "nativeSrc": "7041:16:1", "nodeType": "YulFunctionCall", "src": "7041:16:1"}], "functionName": {"name": "mload", "nativeSrc": "7035:5:1", "nodeType": "YulIdentifier", "src": "7035:5:1"}, "nativeSrc": "7035:23:1", "nodeType": "YulFunctionCall", "src": "7035:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "7019:12:1", "nodeType": "YulTypedName", "src": "7019:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "7105:12:1", "nodeType": "YulIdentifier", "src": "7105:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "7123:3:1", "nodeType": "YulIdentifier", "src": "7123:3:1"}, {"kind": "number", "nativeSrc": "7128:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7128:4:1", "type": "", "value": "0x60"}], "functionName": {"name": "add", "nativeSrc": "7119:3:1", "nodeType": "YulIdentifier", "src": "7119:3:1"}, "nativeSrc": "7119:14:1", "nodeType": "YulFunctionCall", "src": "7119:14:1"}], "functionName": {"name": "abi_encode_t_uint256_to_t_uint256", "nativeSrc": "7071:33:1", "nodeType": "YulIdentifier", "src": "7071:33:1"}, "nativeSrc": "7071:63:1", "nodeType": "YulFunctionCall", "src": "7071:63:1"}, "nativeSrc": "7071:63:1", "nodeType": "YulExpressionStatement", "src": "7071:63:1"}]}, {"nativeSrc": "7154:169:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7154:169:1", "statements": [{"nativeSrc": "7196:43:1", "nodeType": "YulVariableDeclaration", "src": "7196:43:1", "value": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "7226:5:1", "nodeType": "YulIdentifier", "src": "7226:5:1"}, {"kind": "number", "nativeSrc": "7233:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7233:4:1", "type": "", "value": "0x80"}], "functionName": {"name": "add", "nativeSrc": "7222:3:1", "nodeType": "YulIdentifier", "src": "7222:3:1"}, "nativeSrc": "7222:16:1", "nodeType": "YulFunctionCall", "src": "7222:16:1"}], "functionName": {"name": "mload", "nativeSrc": "7216:5:1", "nodeType": "YulIdentifier", "src": "7216:5:1"}, "nativeSrc": "7216:23:1", "nodeType": "YulFunctionCall", "src": "7216:23:1"}, "variables": [{"name": "memberValue0", "nativeSrc": "7200:12:1", "nodeType": "YulTypedName", "src": "7200:12:1", "type": ""}]}, {"expression": {"arguments": [{"name": "memberValue0", "nativeSrc": "7284:12:1", "nodeType": "YulIdentifier", "src": "7284:12:1"}, {"arguments": [{"name": "pos", "nativeSrc": "7302:3:1", "nodeType": "YulIdentifier", "src": "7302:3:1"}, {"kind": "number", "nativeSrc": "7307:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7307:4:1", "type": "", "value": "0x80"}], "functionName": {"name": "add", "nativeSrc": "7298:3:1", "nodeType": "YulIdentifier", "src": "7298:3:1"}, "nativeSrc": "7298:14:1", "nodeType": "YulFunctionCall", "src": "7298:14:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32", "nativeSrc": "7252:31:1", "nodeType": "YulIdentifier", "src": "7252:31:1"}, "nativeSrc": "7252:61:1", "nodeType": "YulFunctionCall", "src": "7252:61:1"}, "nativeSrc": "7252:61:1", "nodeType": "YulExpressionStatement", "src": "7252:61:1"}]}, {"nativeSrc": "7333:11:1", "nodeType": "YulAssignment", "src": "7333:11:1", "value": {"name": "tail", "nativeSrc": "7340:4:1", "nodeType": "YulIdentifier", "src": "7340:4:1"}, "variableNames": [{"name": "end", "nativeSrc": "7333:3:1", "nodeType": "YulIdentifier", "src": "7333:3:1"}]}]}, "name": "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr_fromStack", "nativeSrc": "6210:1140:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "6305:5:1", "nodeType": "YulTypedName", "src": "6305:5:1", "type": ""}, {"name": "pos", "nativeSrc": "6312:3:1", "nodeType": "YulTypedName", "src": "6312:3:1", "type": ""}], "returnVariables": [{"name": "end", "nativeSrc": "6321:3:1", "nodeType": "YulTypedName", "src": "6321:3:1", "type": ""}], "src": "6210:1140:1"}, {"body": {"nativeSrc": "7496:217:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7496:217:1", "statements": [{"nativeSrc": "7506:26:1", "nodeType": "YulAssignment", "src": "7506:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "7518:9:1", "nodeType": "YulIdentifier", "src": "7518:9:1"}, {"kind": "number", "nativeSrc": "7529:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7529:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "7514:3:1", "nodeType": "YulIdentifier", "src": "7514:3:1"}, "nativeSrc": "7514:18:1", "nodeType": "YulFunctionCall", "src": "7514:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "7506:4:1", "nodeType": "YulIdentifier", "src": "7506:4:1"}]}, {"expression": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "7553:9:1", "nodeType": "YulIdentifier", "src": "7553:9:1"}, {"kind": "number", "nativeSrc": "7564:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7564:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "7549:3:1", "nodeType": "YulIdentifier", "src": "7549:3:1"}, "nativeSrc": "7549:17:1", "nodeType": "YulFunctionCall", "src": "7549:17:1"}, {"arguments": [{"name": "tail", "nativeSrc": "7572:4:1", "nodeType": "YulIdentifier", "src": "7572:4:1"}, {"name": "headStart", "nativeSrc": "7578:9:1", "nodeType": "YulIdentifier", "src": "7578:9:1"}], "functionName": {"name": "sub", "nativeSrc": "7568:3:1", "nodeType": "YulIdentifier", "src": "7568:3:1"}, "nativeSrc": "7568:20:1", "nodeType": "YulFunctionCall", "src": "7568:20:1"}], "functionName": {"name": "mstore", "nativeSrc": "7542:6:1", "nodeType": "YulIdentifier", "src": "7542:6:1"}, "nativeSrc": "7542:47:1", "nodeType": "YulFunctionCall", "src": "7542:47:1"}, "nativeSrc": "7542:47:1", "nodeType": "YulExpressionStatement", "src": "7542:47:1"}, {"nativeSrc": "7598:108:1", "nodeType": "YulAssignment", "src": "7598:108:1", "value": {"arguments": [{"name": "value0", "nativeSrc": "7692:6:1", "nodeType": "YulIdentifier", "src": "7692:6:1"}, {"name": "tail", "nativeSrc": "7701:4:1", "nodeType": "YulIdentifier", "src": "7701:4:1"}], "functionName": {"name": "abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr_fromStack", "nativeSrc": "7606:85:1", "nodeType": "YulIdentifier", "src": "7606:85:1"}, "nativeSrc": "7606:100:1", "nodeType": "YulFunctionCall", "src": "7606:100:1"}, "variableNames": [{"name": "tail", "nativeSrc": "7598:4:1", "nodeType": "YulIdentifier", "src": "7598:4:1"}]}]}, "name": "abi_encode_tuple_t_struct$_Price_$31_memory_ptr__to_t_struct$_Price_$31_memory_ptr__fromStack_reversed", "nativeSrc": "7356:357:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "7468:9:1", "nodeType": "YulTypedName", "src": "7468:9:1", "type": ""}, {"name": "value0", "nativeSrc": "7480:6:1", "nodeType": "YulTypedName", "src": "7480:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "7491:4:1", "nodeType": "YulTypedName", "src": "7491:4:1", "type": ""}], "src": "7356:357:1"}, {"body": {"nativeSrc": "7747:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7747:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "7764:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7764:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "7767:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7767:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "7757:6:1", "nodeType": "YulIdentifier", "src": "7757:6:1"}, "nativeSrc": "7757:88:1", "nodeType": "YulFunctionCall", "src": "7757:88:1"}, "nativeSrc": "7757:88:1", "nodeType": "YulExpressionStatement", "src": "7757:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "7861:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7861:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "7864:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7864:4:1", "type": "", "value": "0x32"}], "functionName": {"name": "mstore", "nativeSrc": "7854:6:1", "nodeType": "YulIdentifier", "src": "7854:6:1"}, "nativeSrc": "7854:15:1", "nodeType": "YulFunctionCall", "src": "7854:15:1"}, "nativeSrc": "7854:15:1", "nodeType": "YulExpressionStatement", "src": "7854:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "7885:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7885:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "7888:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "7888:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "7878:6:1", "nodeType": "YulIdentifier", "src": "7878:6:1"}, "nativeSrc": "7878:15:1", "nodeType": "YulFunctionCall", "src": "7878:15:1"}, "nativeSrc": "7878:15:1", "nodeType": "YulExpressionStatement", "src": "7878:15:1"}]}, "name": "panic_error_0x32", "nativeSrc": "7719:180:1", "nodeType": "YulFunctionDefinition", "src": "7719:180:1"}, {"body": {"nativeSrc": "7968:52:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "7968:52:1", "statements": [{"expression": {"arguments": [{"name": "pos", "nativeSrc": "7985:3:1", "nodeType": "YulIdentifier", "src": "7985:3:1"}, {"arguments": [{"name": "value", "nativeSrc": "8007:5:1", "nodeType": "YulIdentifier", "src": "8007:5:1"}], "functionName": {"name": "cleanup_t_uint32", "nativeSrc": "7990:16:1", "nodeType": "YulIdentifier", "src": "7990:16:1"}, "nativeSrc": "7990:23:1", "nodeType": "YulFunctionCall", "src": "7990:23:1"}], "functionName": {"name": "mstore", "nativeSrc": "7978:6:1", "nodeType": "YulIdentifier", "src": "7978:6:1"}, "nativeSrc": "7978:36:1", "nodeType": "YulFunctionCall", "src": "7978:36:1"}, "nativeSrc": "7978:36:1", "nodeType": "YulExpressionStatement", "src": "7978:36:1"}]}, "name": "abi_encode_t_uint32_to_t_uint32_fromStack", "nativeSrc": "7905:115:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "7956:5:1", "nodeType": "YulTypedName", "src": "7956:5:1", "type": ""}, {"name": "pos", "nativeSrc": "7963:3:1", "nodeType": "YulTypedName", "src": "7963:3:1", "type": ""}], "src": "7905:115:1"}, {"body": {"nativeSrc": "8122:122:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8122:122:1", "statements": [{"nativeSrc": "8132:26:1", "nodeType": "YulAssignment", "src": "8132:26:1", "value": {"arguments": [{"name": "headStart", "nativeSrc": "8144:9:1", "nodeType": "YulIdentifier", "src": "8144:9:1"}, {"kind": "number", "nativeSrc": "8155:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8155:2:1", "type": "", "value": "32"}], "functionName": {"name": "add", "nativeSrc": "8140:3:1", "nodeType": "YulIdentifier", "src": "8140:3:1"}, "nativeSrc": "8140:18:1", "nodeType": "YulFunctionCall", "src": "8140:18:1"}, "variableNames": [{"name": "tail", "nativeSrc": "8132:4:1", "nodeType": "YulIdentifier", "src": "8132:4:1"}]}, {"expression": {"arguments": [{"name": "value0", "nativeSrc": "8210:6:1", "nodeType": "YulIdentifier", "src": "8210:6:1"}, {"arguments": [{"name": "headStart", "nativeSrc": "8223:9:1", "nodeType": "YulIdentifier", "src": "8223:9:1"}, {"kind": "number", "nativeSrc": "8234:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8234:1:1", "type": "", "value": "0"}], "functionName": {"name": "add", "nativeSrc": "8219:3:1", "nodeType": "YulIdentifier", "src": "8219:3:1"}, "nativeSrc": "8219:17:1", "nodeType": "YulFunctionCall", "src": "8219:17:1"}], "functionName": {"name": "abi_encode_t_uint32_to_t_uint32_fromStack", "nativeSrc": "8168:41:1", "nodeType": "YulIdentifier", "src": "8168:41:1"}, "nativeSrc": "8168:69:1", "nodeType": "YulFunctionCall", "src": "8168:69:1"}, "nativeSrc": "8168:69:1", "nodeType": "YulExpressionStatement", "src": "8168:69:1"}]}, "name": "abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed", "nativeSrc": "8026:218:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "8094:9:1", "nodeType": "YulTypedName", "src": "8094:9:1", "type": ""}, {"name": "value0", "nativeSrc": "8106:6:1", "nodeType": "YulTypedName", "src": "8106:6:1", "type": ""}], "returnVariables": [{"name": "tail", "nativeSrc": "8117:4:1", "nodeType": "YulTypedName", "src": "8117:4:1", "type": ""}], "src": "8026:218:1"}, {"body": {"nativeSrc": "8292:78:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8292:78:1", "statements": [{"body": {"nativeSrc": "8348:16:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8348:16:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "8357:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8357:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "8360:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8360:1:1", "type": "", "value": "0"}], "functionName": {"name": "revert", "nativeSrc": "8350:6:1", "nodeType": "YulIdentifier", "src": "8350:6:1"}, "nativeSrc": "8350:12:1", "nodeType": "YulFunctionCall", "src": "8350:12:1"}, "nativeSrc": "8350:12:1", "nodeType": "YulExpressionStatement", "src": "8350:12:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "value", "nativeSrc": "8315:5:1", "nodeType": "YulIdentifier", "src": "8315:5:1"}, {"arguments": [{"name": "value", "nativeSrc": "8339:5:1", "nodeType": "YulIdentifier", "src": "8339:5:1"}], "functionName": {"name": "cleanup_t_uint64", "nativeSrc": "8322:16:1", "nodeType": "YulIdentifier", "src": "8322:16:1"}, "nativeSrc": "8322:23:1", "nodeType": "YulFunctionCall", "src": "8322:23:1"}], "functionName": {"name": "eq", "nativeSrc": "8312:2:1", "nodeType": "YulIdentifier", "src": "8312:2:1"}, "nativeSrc": "8312:34:1", "nodeType": "YulFunctionCall", "src": "8312:34:1"}], "functionName": {"name": "iszero", "nativeSrc": "8305:6:1", "nodeType": "YulIdentifier", "src": "8305:6:1"}, "nativeSrc": "8305:42:1", "nodeType": "YulFunctionCall", "src": "8305:42:1"}, "nativeSrc": "8302:62:1", "nodeType": "YulIf", "src": "8302:62:1"}]}, "name": "validator_revert_t_uint64", "nativeSrc": "8250:120:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "value", "nativeSrc": "8285:5:1", "nodeType": "YulTypedName", "src": "8285:5:1", "type": ""}], "src": "8250:120:1"}, {"body": {"nativeSrc": "8438:79:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8438:79:1", "statements": [{"nativeSrc": "8448:22:1", "nodeType": "YulAssignment", "src": "8448:22:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "8463:6:1", "nodeType": "YulIdentifier", "src": "8463:6:1"}], "functionName": {"name": "mload", "nativeSrc": "8457:5:1", "nodeType": "YulIdentifier", "src": "8457:5:1"}, "nativeSrc": "8457:13:1", "nodeType": "YulFunctionCall", "src": "8457:13:1"}, "variableNames": [{"name": "value", "nativeSrc": "8448:5:1", "nodeType": "YulIdentifier", "src": "8448:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "8505:5:1", "nodeType": "YulIdentifier", "src": "8505:5:1"}], "functionName": {"name": "validator_revert_t_uint64", "nativeSrc": "8479:25:1", "nodeType": "YulIdentifier", "src": "8479:25:1"}, "nativeSrc": "8479:32:1", "nodeType": "YulFunctionCall", "src": "8479:32:1"}, "nativeSrc": "8479:32:1", "nodeType": "YulExpressionStatement", "src": "8479:32:1"}]}, "name": "abi_decode_t_uint64_fromMemory", "nativeSrc": "8376:141:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "8416:6:1", "nodeType": "YulTypedName", "src": "8416:6:1", "type": ""}, {"name": "end", "nativeSrc": "8424:3:1", "nodeType": "YulTypedName", "src": "8424:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "8432:5:1", "nodeType": "YulTypedName", "src": "8432:5:1", "type": ""}], "src": "8376:141:1"}, {"body": {"nativeSrc": "8599:273:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8599:273:1", "statements": [{"body": {"nativeSrc": "8645:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8645:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "8647:77:1", "nodeType": "YulIdentifier", "src": "8647:77:1"}, "nativeSrc": "8647:79:1", "nodeType": "YulFunctionCall", "src": "8647:79:1"}, "nativeSrc": "8647:79:1", "nodeType": "YulExpressionStatement", "src": "8647:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "8620:7:1", "nodeType": "YulIdentifier", "src": "8620:7:1"}, {"name": "headStart", "nativeSrc": "8629:9:1", "nodeType": "YulIdentifier", "src": "8629:9:1"}], "functionName": {"name": "sub", "nativeSrc": "8616:3:1", "nodeType": "YulIdentifier", "src": "8616:3:1"}, "nativeSrc": "8616:23:1", "nodeType": "YulFunctionCall", "src": "8616:23:1"}, {"kind": "number", "nativeSrc": "8641:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8641:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "8612:3:1", "nodeType": "YulIdentifier", "src": "8612:3:1"}, "nativeSrc": "8612:32:1", "nodeType": "YulFunctionCall", "src": "8612:32:1"}, "nativeSrc": "8609:119:1", "nodeType": "YulIf", "src": "8609:119:1"}, {"nativeSrc": "8738:127:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8738:127:1", "statements": [{"nativeSrc": "8753:15:1", "nodeType": "YulVariableDeclaration", "src": "8753:15:1", "value": {"kind": "number", "nativeSrc": "8767:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "8767:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "8757:6:1", "nodeType": "YulTypedName", "src": "8757:6:1", "type": ""}]}, {"nativeSrc": "8782:73:1", "nodeType": "YulAssignment", "src": "8782:73:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "8827:9:1", "nodeType": "YulIdentifier", "src": "8827:9:1"}, {"name": "offset", "nativeSrc": "8838:6:1", "nodeType": "YulIdentifier", "src": "8838:6:1"}], "functionName": {"name": "add", "nativeSrc": "8823:3:1", "nodeType": "YulIdentifier", "src": "8823:3:1"}, "nativeSrc": "8823:22:1", "nodeType": "YulFunctionCall", "src": "8823:22:1"}, {"name": "dataEnd", "nativeSrc": "8847:7:1", "nodeType": "YulIdentifier", "src": "8847:7:1"}], "functionName": {"name": "abi_decode_t_uint64_fromMemory", "nativeSrc": "8792:30:1", "nodeType": "YulIdentifier", "src": "8792:30:1"}, "nativeSrc": "8792:63:1", "nodeType": "YulFunctionCall", "src": "8792:63:1"}, "variableNames": [{"name": "value0", "nativeSrc": "8782:6:1", "nodeType": "YulIdentifier", "src": "8782:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint64_fromMemory", "nativeSrc": "8523:349:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "8569:9:1", "nodeType": "YulTypedName", "src": "8569:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "8580:7:1", "nodeType": "YulTypedName", "src": "8580:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "8592:6:1", "nodeType": "YulTypedName", "src": "8592:6:1", "type": ""}], "src": "8523:349:1"}, {"body": {"nativeSrc": "8940:79:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "8940:79:1", "statements": [{"nativeSrc": "8950:22:1", "nodeType": "YulAssignment", "src": "8950:22:1", "value": {"arguments": [{"name": "offset", "nativeSrc": "8965:6:1", "nodeType": "YulIdentifier", "src": "8965:6:1"}], "functionName": {"name": "mload", "nativeSrc": "8959:5:1", "nodeType": "YulIdentifier", "src": "8959:5:1"}, "nativeSrc": "8959:13:1", "nodeType": "YulFunctionCall", "src": "8959:13:1"}, "variableNames": [{"name": "value", "nativeSrc": "8950:5:1", "nodeType": "YulIdentifier", "src": "8950:5:1"}]}, {"expression": {"arguments": [{"name": "value", "nativeSrc": "9007:5:1", "nodeType": "YulIdentifier", "src": "9007:5:1"}], "functionName": {"name": "validator_revert_t_uint32", "nativeSrc": "8981:25:1", "nodeType": "YulIdentifier", "src": "8981:25:1"}, "nativeSrc": "8981:32:1", "nodeType": "YulFunctionCall", "src": "8981:32:1"}, "nativeSrc": "8981:32:1", "nodeType": "YulExpressionStatement", "src": "8981:32:1"}]}, "name": "abi_decode_t_uint32_fromMemory", "nativeSrc": "8878:141:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "offset", "nativeSrc": "8918:6:1", "nodeType": "YulTypedName", "src": "8918:6:1", "type": ""}, {"name": "end", "nativeSrc": "8926:3:1", "nodeType": "YulTypedName", "src": "8926:3:1", "type": ""}], "returnVariables": [{"name": "value", "nativeSrc": "8934:5:1", "nodeType": "YulTypedName", "src": "8934:5:1", "type": ""}], "src": "8878:141:1"}, {"body": {"nativeSrc": "9101:273:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9101:273:1", "statements": [{"body": {"nativeSrc": "9147:83:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9147:83:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b", "nativeSrc": "9149:77:1", "nodeType": "YulIdentifier", "src": "9149:77:1"}, "nativeSrc": "9149:79:1", "nodeType": "YulFunctionCall", "src": "9149:79:1"}, "nativeSrc": "9149:79:1", "nodeType": "YulExpressionStatement", "src": "9149:79:1"}]}, "condition": {"arguments": [{"arguments": [{"name": "dataEnd", "nativeSrc": "9122:7:1", "nodeType": "YulIdentifier", "src": "9122:7:1"}, {"name": "headStart", "nativeSrc": "9131:9:1", "nodeType": "YulIdentifier", "src": "9131:9:1"}], "functionName": {"name": "sub", "nativeSrc": "9118:3:1", "nodeType": "YulIdentifier", "src": "9118:3:1"}, "nativeSrc": "9118:23:1", "nodeType": "YulFunctionCall", "src": "9118:23:1"}, {"kind": "number", "nativeSrc": "9143:2:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9143:2:1", "type": "", "value": "32"}], "functionName": {"name": "slt", "nativeSrc": "9114:3:1", "nodeType": "YulIdentifier", "src": "9114:3:1"}, "nativeSrc": "9114:32:1", "nodeType": "YulFunctionCall", "src": "9114:32:1"}, "nativeSrc": "9111:119:1", "nodeType": "YulIf", "src": "9111:119:1"}, {"nativeSrc": "9240:127:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9240:127:1", "statements": [{"nativeSrc": "9255:15:1", "nodeType": "YulVariableDeclaration", "src": "9255:15:1", "value": {"kind": "number", "nativeSrc": "9269:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9269:1:1", "type": "", "value": "0"}, "variables": [{"name": "offset", "nativeSrc": "9259:6:1", "nodeType": "YulTypedName", "src": "9259:6:1", "type": ""}]}, {"nativeSrc": "9284:73:1", "nodeType": "YulAssignment", "src": "9284:73:1", "value": {"arguments": [{"arguments": [{"name": "headStart", "nativeSrc": "9329:9:1", "nodeType": "YulIdentifier", "src": "9329:9:1"}, {"name": "offset", "nativeSrc": "9340:6:1", "nodeType": "YulIdentifier", "src": "9340:6:1"}], "functionName": {"name": "add", "nativeSrc": "9325:3:1", "nodeType": "YulIdentifier", "src": "9325:3:1"}, "nativeSrc": "9325:22:1", "nodeType": "YulFunctionCall", "src": "9325:22:1"}, {"name": "dataEnd", "nativeSrc": "9349:7:1", "nodeType": "YulIdentifier", "src": "9349:7:1"}], "functionName": {"name": "abi_decode_t_uint32_fromMemory", "nativeSrc": "9294:30:1", "nodeType": "YulIdentifier", "src": "9294:30:1"}, "nativeSrc": "9294:63:1", "nodeType": "YulFunctionCall", "src": "9294:63:1"}, "variableNames": [{"name": "value0", "nativeSrc": "9284:6:1", "nodeType": "YulIdentifier", "src": "9284:6:1"}]}]}]}, "name": "abi_decode_tuple_t_uint32_fromMemory", "nativeSrc": "9025:349:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "headStart", "nativeSrc": "9071:9:1", "nodeType": "YulTypedName", "src": "9071:9:1", "type": ""}, {"name": "dataEnd", "nativeSrc": "9082:7:1", "nodeType": "YulTypedName", "src": "9082:7:1", "type": ""}], "returnVariables": [{"name": "value0", "nativeSrc": "9094:6:1", "nodeType": "YulTypedName", "src": "9094:6:1", "type": ""}], "src": "9025:349:1"}, {"body": {"nativeSrc": "9408:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9408:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "9425:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9425:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9428:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9428:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "9418:6:1", "nodeType": "YulIdentifier", "src": "9418:6:1"}, "nativeSrc": "9418:88:1", "nodeType": "YulFunctionCall", "src": "9418:88:1"}, "nativeSrc": "9418:88:1", "nodeType": "YulExpressionStatement", "src": "9418:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9522:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9522:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "9525:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9525:4:1", "type": "", "value": "0x12"}], "functionName": {"name": "mstore", "nativeSrc": "9515:6:1", "nodeType": "YulIdentifier", "src": "9515:6:1"}, "nativeSrc": "9515:15:1", "nodeType": "YulFunctionCall", "src": "9515:15:1"}, "nativeSrc": "9515:15:1", "nodeType": "YulExpressionStatement", "src": "9515:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9546:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9546:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9549:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9549:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "9539:6:1", "nodeType": "YulIdentifier", "src": "9539:6:1"}, "nativeSrc": "9539:15:1", "nodeType": "YulFunctionCall", "src": "9539:15:1"}, "nativeSrc": "9539:15:1", "nodeType": "YulExpressionStatement", "src": "9539:15:1"}]}, "name": "panic_error_0x12", "nativeSrc": "9380:180:1", "nodeType": "YulFunctionDefinition", "src": "9380:180:1"}, {"body": {"nativeSrc": "9594:152:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9594:152:1", "statements": [{"expression": {"arguments": [{"kind": "number", "nativeSrc": "9611:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9611:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9614:77:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9614:77:1", "type": "", "value": "35408467139433450592217433187231851964531694900788300625387963629091585785856"}], "functionName": {"name": "mstore", "nativeSrc": "9604:6:1", "nodeType": "YulIdentifier", "src": "9604:6:1"}, "nativeSrc": "9604:88:1", "nodeType": "YulFunctionCall", "src": "9604:88:1"}, "nativeSrc": "9604:88:1", "nodeType": "YulExpressionStatement", "src": "9604:88:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9708:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9708:1:1", "type": "", "value": "4"}, {"kind": "number", "nativeSrc": "9711:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9711:4:1", "type": "", "value": "0x11"}], "functionName": {"name": "mstore", "nativeSrc": "9701:6:1", "nodeType": "YulIdentifier", "src": "9701:6:1"}, "nativeSrc": "9701:15:1", "nodeType": "YulFunctionCall", "src": "9701:15:1"}, "nativeSrc": "9701:15:1", "nodeType": "YulExpressionStatement", "src": "9701:15:1"}, {"expression": {"arguments": [{"kind": "number", "nativeSrc": "9732:1:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9732:1:1", "type": "", "value": "0"}, {"kind": "number", "nativeSrc": "9735:4:1", "nodeType": "Yu<PERSON><PERSON><PERSON><PERSON>", "src": "9735:4:1", "type": "", "value": "0x24"}], "functionName": {"name": "revert", "nativeSrc": "9725:6:1", "nodeType": "YulIdentifier", "src": "9725:6:1"}, "nativeSrc": "9725:15:1", "nodeType": "YulFunctionCall", "src": "9725:15:1"}, "nativeSrc": "9725:15:1", "nodeType": "YulExpressionStatement", "src": "9725:15:1"}]}, "name": "panic_error_0x11", "nativeSrc": "9566:180:1", "nodeType": "YulFunctionDefinition", "src": "9566:180:1"}, {"body": {"nativeSrc": "9794:143:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9794:143:1", "statements": [{"nativeSrc": "9804:25:1", "nodeType": "YulAssignment", "src": "9804:25:1", "value": {"arguments": [{"name": "x", "nativeSrc": "9827:1:1", "nodeType": "YulIdentifier", "src": "9827:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "9809:17:1", "nodeType": "YulIdentifier", "src": "9809:17:1"}, "nativeSrc": "9809:20:1", "nodeType": "YulFunctionCall", "src": "9809:20:1"}, "variableNames": [{"name": "x", "nativeSrc": "9804:1:1", "nodeType": "YulIdentifier", "src": "9804:1:1"}]}, {"nativeSrc": "9838:25:1", "nodeType": "YulAssignment", "src": "9838:25:1", "value": {"arguments": [{"name": "y", "nativeSrc": "9861:1:1", "nodeType": "YulIdentifier", "src": "9861:1:1"}], "functionName": {"name": "cleanup_t_uint256", "nativeSrc": "9843:17:1", "nodeType": "YulIdentifier", "src": "9843:17:1"}, "nativeSrc": "9843:20:1", "nodeType": "YulFunctionCall", "src": "9843:20:1"}, "variableNames": [{"name": "y", "nativeSrc": "9838:1:1", "nodeType": "YulIdentifier", "src": "9838:1:1"}]}, {"body": {"nativeSrc": "9885:22:1", "nodeType": "<PERSON><PERSON><PERSON><PERSON>", "src": "9885:22:1", "statements": [{"expression": {"arguments": [], "functionName": {"name": "panic_error_0x12", "nativeSrc": "9887:16:1", "nodeType": "YulIdentifier", "src": "9887:16:1"}, "nativeSrc": "9887:18:1", "nodeType": "YulFunctionCall", "src": "9887:18:1"}, "nativeSrc": "9887:18:1", "nodeType": "YulExpressionStatement", "src": "9887:18:1"}]}, "condition": {"arguments": [{"name": "y", "nativeSrc": "9882:1:1", "nodeType": "YulIdentifier", "src": "9882:1:1"}], "functionName": {"name": "iszero", "nativeSrc": "9875:6:1", "nodeType": "YulIdentifier", "src": "9875:6:1"}, "nativeSrc": "9875:9:1", "nodeType": "YulFunctionCall", "src": "9875:9:1"}, "nativeSrc": "9872:35:1", "nodeType": "YulIf", "src": "9872:35:1"}, {"nativeSrc": "9917:14:1", "nodeType": "YulAssignment", "src": "9917:14:1", "value": {"arguments": [{"name": "x", "nativeSrc": "9926:1:1", "nodeType": "YulIdentifier", "src": "9926:1:1"}, {"name": "y", "nativeSrc": "9929:1:1", "nodeType": "YulIdentifier", "src": "9929:1:1"}], "functionName": {"name": "div", "nativeSrc": "9922:3:1", "nodeType": "YulIdentifier", "src": "9922:3:1"}, "nativeSrc": "9922:9:1", "nodeType": "YulFunctionCall", "src": "9922:9:1"}, "variableNames": [{"name": "r", "nativeSrc": "9917:1:1", "nodeType": "YulIdentifier", "src": "9917:1:1"}]}]}, "name": "checked_div_t_uint256", "nativeSrc": "9752:185:1", "nodeType": "YulFunctionDefinition", "parameters": [{"name": "x", "nativeSrc": "9783:1:1", "nodeType": "YulTypedName", "src": "9783:1:1", "type": ""}, {"name": "y", "nativeSrc": "9786:1:1", "nodeType": "YulTypedName", "src": "9786:1:1", "type": ""}], "returnVariables": [{"name": "r", "nativeSrc": "9792:1:1", "nodeType": "YulTypedName", "src": "9792:1:1", "type": ""}], "src": "9752:185:1"}]}, "contents": "{\n\n    function array_length_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(value) -> length {\n\n        length := 0x05\n\n    }\n\n    function array_storeLengthForEncoding_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack(pos, length) -> updated_pos {\n        updated_pos := pos\n    }\n\n    function array_dataslot_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(ptr) -> data {\n        data := ptr\n\n    }\n\n    function array_length_t_string_memory_ptr(value) -> length {\n\n        length := mload(value)\n\n    }\n\n    function array_storeLengthForEncoding_t_string_memory_ptr(pos, length) -> updated_pos {\n        mstore(pos, length)\n        updated_pos := add(pos, 0x20)\n    }\n\n    function copy_memory_to_memory_with_cleanup(src, dst, length) {\n\n        let i := 0\n        for { } lt(i, length) { i := add(i, 32) }\n        {\n            mstore(add(dst, i), mload(add(src, i)))\n        }\n        mstore(add(dst, length), 0)\n\n    }\n\n    function round_up_to_mul_of_32(value) -> result {\n        result := and(add(value, 31), not(31))\n    }\n\n    function abi_encode_t_string_memory_ptr_to_t_string_memory_ptr(value, pos) -> end {\n        let length := array_length_t_string_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_string_memory_ptr(pos, length)\n        copy_memory_to_memory_with_cleanup(add(value, 0x20), pos, length)\n        end := add(pos, round_up_to_mul_of_32(length))\n    }\n\n    function cleanup_t_uint32(value) -> cleaned {\n        cleaned := and(value, 0xffffffff)\n    }\n\n    function abi_encode_t_uint32_to_t_uint32(value, pos) {\n        mstore(pos, cleanup_t_uint32(value))\n    }\n\n    function cleanup_t_uint64(value) -> cleaned {\n        cleaned := and(value, 0xffffffffffffffff)\n    }\n\n    function abi_encode_t_uint64_to_t_uint64(value, pos) {\n        mstore(pos, cleanup_t_uint64(value))\n    }\n\n    function cleanup_t_uint256(value) -> cleaned {\n        cleaned := value\n    }\n\n    function abi_encode_t_uint256_to_t_uint256(value, pos) {\n        mstore(pos, cleanup_t_uint256(value))\n    }\n\n    // struct ReadPrices.Price -> struct ReadPrices.Price\n    function abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr(value, pos)  -> end  {\n        let tail := add(pos, 0xa0)\n\n        {\n            // symbol\n\n            let memberValue0 := mload(add(value, 0x00))\n\n            mstore(add(pos, 0x00), sub(tail, pos))\n            tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr(memberValue0, tail)\n\n        }\n\n        {\n            // assetId\n\n            let memberValue0 := mload(add(value, 0x20))\n            abi_encode_t_uint32_to_t_uint32(memberValue0, add(pos, 0x20))\n        }\n\n        {\n            // rawPrice\n\n            let memberValue0 := mload(add(value, 0x40))\n            abi_encode_t_uint64_to_t_uint64(memberValue0, add(pos, 0x40))\n        }\n\n        {\n            // formattedPrice\n\n            let memberValue0 := mload(add(value, 0x60))\n            abi_encode_t_uint256_to_t_uint256(memberValue0, add(pos, 0x60))\n        }\n\n        {\n            // blockNumber\n\n            let memberValue0 := mload(add(value, 0x80))\n            abi_encode_t_uint32_to_t_uint32(memberValue0, add(pos, 0x80))\n        }\n\n        end := tail\n    }\n\n    function abi_encodeUpdatedPos_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr(value0, pos) -> updatedPos {\n        updatedPos := abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr(value0, pos)\n    }\n\n    function array_nextElement_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(ptr) -> next {\n        next := add(ptr, 0x20)\n    }\n\n    // struct ReadPrices.Price[5] -> struct ReadPrices.Price[5]\n    function abi_encode_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack(value, pos)  -> end  {\n        let length := array_length_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(value)\n        pos := array_storeLengthForEncoding_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack(pos, length)\n        let headStart := pos\n        let tail := add(pos, mul(length, 0x20))\n        let baseRef := array_dataslot_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(value)\n        let srcPtr := baseRef\n        for { let i := 0 } lt(i, length) { i := add(i, 1) }\n        {\n            mstore(pos, sub(tail, headStart))\n            let elementValue0 := mload(srcPtr)\n            tail := abi_encodeUpdatedPos_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr(elementValue0, tail)\n            srcPtr := array_nextElement_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr(srcPtr)\n            pos := add(pos, 0x20)\n        }\n        pos := tail\n        end := pos\n    }\n\n    function abi_encode_tuple_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_to_t_array$_t_struct$_Price_$31_memory_ptr_$5_memory_ptr_fromStack(value0,  tail)\n\n    }\n\n    function allocate_unbounded() -> memPtr {\n        memPtr := mload(64)\n    }\n\n    function revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() {\n        revert(0, 0)\n    }\n\n    function revert_error_c1322bf8034eace5e0b5c7295db60986aa89aae5e0ea0873e4689e076861a5db() {\n        revert(0, 0)\n    }\n\n    function validator_revert_t_uint32(value) {\n        if iszero(eq(value, cleanup_t_uint32(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint32(offset, end) -> value {\n        value := calldataload(offset)\n        validator_revert_t_uint32(value)\n    }\n\n    function abi_decode_tuple_t_uint32(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint32(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    // struct ReadPrices.Price -> struct ReadPrices.Price\n    function abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr_fromStack(value, pos)  -> end  {\n        let tail := add(pos, 0xa0)\n\n        {\n            // symbol\n\n            let memberValue0 := mload(add(value, 0x00))\n\n            mstore(add(pos, 0x00), sub(tail, pos))\n            tail := abi_encode_t_string_memory_ptr_to_t_string_memory_ptr(memberValue0, tail)\n\n        }\n\n        {\n            // assetId\n\n            let memberValue0 := mload(add(value, 0x20))\n            abi_encode_t_uint32_to_t_uint32(memberValue0, add(pos, 0x20))\n        }\n\n        {\n            // rawPrice\n\n            let memberValue0 := mload(add(value, 0x40))\n            abi_encode_t_uint64_to_t_uint64(memberValue0, add(pos, 0x40))\n        }\n\n        {\n            // formattedPrice\n\n            let memberValue0 := mload(add(value, 0x60))\n            abi_encode_t_uint256_to_t_uint256(memberValue0, add(pos, 0x60))\n        }\n\n        {\n            // blockNumber\n\n            let memberValue0 := mload(add(value, 0x80))\n            abi_encode_t_uint32_to_t_uint32(memberValue0, add(pos, 0x80))\n        }\n\n        end := tail\n    }\n\n    function abi_encode_tuple_t_struct$_Price_$31_memory_ptr__to_t_struct$_Price_$31_memory_ptr__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        mstore(add(headStart, 0), sub(tail, headStart))\n        tail := abi_encode_t_struct$_Price_$31_memory_ptr_to_t_struct$_Price_$31_memory_ptr_fromStack(value0,  tail)\n\n    }\n\n    function panic_error_0x32() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x32)\n        revert(0, 0x24)\n    }\n\n    function abi_encode_t_uint32_to_t_uint32_fromStack(value, pos) {\n        mstore(pos, cleanup_t_uint32(value))\n    }\n\n    function abi_encode_tuple_t_uint32__to_t_uint32__fromStack_reversed(headStart , value0) -> tail {\n        tail := add(headStart, 32)\n\n        abi_encode_t_uint32_to_t_uint32_fromStack(value0,  add(headStart, 0))\n\n    }\n\n    function validator_revert_t_uint64(value) {\n        if iszero(eq(value, cleanup_t_uint64(value))) { revert(0, 0) }\n    }\n\n    function abi_decode_t_uint64_fromMemory(offset, end) -> value {\n        value := mload(offset)\n        validator_revert_t_uint64(value)\n    }\n\n    function abi_decode_tuple_t_uint64_fromMemory(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint64_fromMemory(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function abi_decode_t_uint32_fromMemory(offset, end) -> value {\n        value := mload(offset)\n        validator_revert_t_uint32(value)\n    }\n\n    function abi_decode_tuple_t_uint32_fromMemory(headStart, dataEnd) -> value0 {\n        if slt(sub(dataEnd, headStart), 32) { revert_error_dbdddcbe895c83990c08b3492a0e83918d802a52331272ac6fdb6a7c4aea3b1b() }\n\n        {\n\n            let offset := 0\n\n            value0 := abi_decode_t_uint32_fromMemory(add(headStart, offset), dataEnd)\n        }\n\n    }\n\n    function panic_error_0x12() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x12)\n        revert(0, 0x24)\n    }\n\n    function panic_error_0x11() {\n        mstore(0, 35408467139433450592217433187231851964531694900788300625387963629091585785856)\n        mstore(4, 0x11)\n        revert(0, 0x24)\n    }\n\n    function checked_div_t_uint256(x, y) -> r {\n        x := cleanup_t_uint256(x)\n        y := cleanup_t_uint256(y)\n        if iszero(y) { panic_error_0x12() }\n\n        r := div(x, y)\n    }\n\n}\n", "id": 1, "language": "<PERSON>l", "name": "#utility.yul"}], "immutableReferences": {}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 MSTORE CALLVALUE DUP1 ISZERO PUSH2 0x10 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP PUSH1 0x4 CALLDATASIZE LT PUSH2 0x36 JUMPI PUSH1 0x0 CALLDATALOAD PUSH1 0xE0 SHR DUP1 PUSH4 0x445DF9D6 EQ PUSH2 0x3B JUMPI DUP1 PUSH4 0xDA26663A EQ PUSH2 0x59 JUMPI JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x43 PUSH2 0x89 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x50 SWAP2 SWAP1 PUSH2 0x85A JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x73 PUSH1 0x4 DUP1 CALLDATASIZE SUB DUP2 ADD SWAP1 PUSH2 0x6E SWAP2 SWAP1 PUSH2 0x8AD JUMP JUMPDEST PUSH2 0x2AD JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH2 0x80 SWAP2 SWAP1 PUSH2 0x950 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 SWAP2 SUB SWAP1 RETURN JUMPDEST PUSH2 0x91 PUSH2 0x5D1 JUMP JUMPDEST PUSH2 0x99 PUSH2 0x5D1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x0 PUSH4 0xFFFFFFFF AND PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x1 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x2 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x3 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x4 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP1 POP PUSH1 0x0 JUMPDEST PUSH1 0x5 DUP2 LT ISZERO PUSH2 0x2A4 JUMPI PUSH1 0x0 PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0xC0F0F5EF DUP5 DUP5 PUSH1 0x5 DUP2 LT PUSH2 0x12C JUMPI PUSH2 0x12B PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH1 0x40 MLOAD DUP3 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x14D SWAP2 SWAP1 PUSH2 0x9B0 JUMP JUMPDEST PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x16A JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x18E SWAP2 SWAP1 PUSH2 0x9F7 JUMP JUMPDEST SWAP1 POP PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH2 0x1BB DUP6 DUP6 PUSH1 0x5 DUP2 LT PUSH2 0x1B1 JUMPI PUSH2 0x1B0 PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH2 0x3FA JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD DUP5 DUP5 PUSH1 0x5 DUP2 LT PUSH2 0x1D3 JUMPI PUSH2 0x1D2 PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD MLOAD PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x1FC DUP4 PUSH2 0x5B1 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0x298C9005 PUSH1 0x40 MLOAD DUP2 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x24E JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x272 SWAP2 SWAP1 PUSH2 0xA39 JUMP JUMPDEST PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP DUP5 DUP4 PUSH1 0x5 DUP2 LT PUSH2 0x28E JUMPI PUSH2 0x28D PUSH2 0x972 JUMP JUMPDEST JUMPDEST PUSH1 0x20 MUL ADD DUP2 SWAP1 MSTORE POP POP DUP1 DUP1 PUSH1 0x1 ADD SWAP2 POP POP PUSH2 0xF0 JUMP JUMPDEST POP DUP2 SWAP3 POP POP POP SWAP1 JUMP JUMPDEST PUSH2 0x2B5 PUSH2 0x5FE JUMP JUMPDEST PUSH1 0x0 PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0xC0F0F5EF DUP5 PUSH1 0x40 MLOAD DUP3 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH2 0x2F2 SWAP2 SWAP1 PUSH2 0x9B0 JUMP JUMPDEST PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x30F JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x333 SWAP2 SWAP1 PUSH2 0x9F7 JUMP JUMPDEST SWAP1 POP PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH2 0x349 DUP6 PUSH2 0x3FA JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD DUP5 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x373 DUP4 PUSH2 0x5B1 JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD PUSH2 0x800 PUSH20 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF AND PUSH4 0x298C9005 PUSH1 0x40 MLOAD DUP2 PUSH4 0xFFFFFFFF AND PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 ADD PUSH1 0x20 PUSH1 0x40 MLOAD DUP1 DUP4 SUB DUP2 DUP7 GAS STATICCALL ISZERO DUP1 ISZERO PUSH2 0x3C5 JUMPI RETURNDATASIZE PUSH1 0x0 DUP1 RETURNDATACOPY RETURNDATASIZE PUSH1 0x0 REVERT JUMPDEST POP POP POP POP PUSH1 0x40 MLOAD RETURNDATASIZE PUSH1 0x1F NOT PUSH1 0x1F DUP3 ADD AND DUP3 ADD DUP1 PUSH1 0x40 MSTORE POP DUP2 ADD SWAP1 PUSH2 0x3E9 SWAP2 SWAP1 PUSH2 0xA39 JUMP JUMPDEST PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP2 POP POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x60 PUSH1 0x0 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x447 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4254430000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x1 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x492 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4554480000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x2 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x4DD JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x3 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x534F4C0000000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x3 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x528 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x4 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4156415800000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x4 DUP3 PUSH4 0xFFFFFFFF AND SUB PUSH2 0x573 JUMPI PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x4 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x4859504500000000000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP PUSH2 0x5AC JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0x40 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x7 DUP2 MSTORE PUSH1 0x20 ADD PUSH32 0x554E4B4E4F574E00000000000000000000000000000000000000000000000000 DUP2 MSTORE POP SWAP1 POP JUMPDEST SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x64 DUP3 PUSH8 0xFFFFFFFFFFFFFFFF AND PUSH2 0x5CA SWAP2 SWAP1 PUSH2 0xA95 JUMP JUMPDEST SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x5 SWAP1 JUMPDEST PUSH2 0x5E8 PUSH2 0x5FE JUMP JUMPDEST DUP2 MSTORE PUSH1 0x20 ADD SWAP1 PUSH1 0x1 SWAP1 SUB SWAP1 DUP2 PUSH2 0x5E0 JUMPI SWAP1 POP POP SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD DUP1 PUSH1 0xA0 ADD PUSH1 0x40 MSTORE DUP1 PUSH1 0x60 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH4 0xFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF AND DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 DUP2 MSTORE PUSH1 0x20 ADD PUSH1 0x0 PUSH4 0xFFFFFFFF AND DUP2 MSTORE POP SWAP1 JUMP JUMPDEST PUSH1 0x0 PUSH1 0x5 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 DUP3 DUP3 MSTORE PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 JUMPDEST DUP4 DUP2 LT ISZERO PUSH2 0x69D JUMPI DUP1 DUP3 ADD MLOAD DUP2 DUP5 ADD MSTORE PUSH1 0x20 DUP2 ADD SWAP1 POP PUSH2 0x682 JUMP JUMPDEST PUSH1 0x0 DUP5 DUP5 ADD MSTORE POP POP POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x1F NOT PUSH1 0x1F DUP4 ADD AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x6C5 DUP3 PUSH2 0x663 JUMP JUMPDEST PUSH2 0x6CF DUP2 DUP6 PUSH2 0x66E JUMP JUMPDEST SWAP4 POP PUSH2 0x6DF DUP2 DUP6 PUSH1 0x20 DUP7 ADD PUSH2 0x67F JUMP JUMPDEST PUSH2 0x6E8 DUP2 PUSH2 0x6A9 JUMP JUMPDEST DUP5 ADD SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH4 0xFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x70C DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH8 0xFFFFFFFFFFFFFFFF DUP3 AND SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x72F DUP2 PUSH2 0x712 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH2 0x748 DUP2 PUSH2 0x735 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0xA0 DUP4 ADD PUSH1 0x0 DUP4 ADD MLOAD DUP5 DUP3 SUB PUSH1 0x0 DUP7 ADD MSTORE PUSH2 0x76B DUP3 DUP3 PUSH2 0x6BA JUMP JUMPDEST SWAP2 POP POP PUSH1 0x20 DUP4 ADD MLOAD PUSH2 0x780 PUSH1 0x20 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP PUSH1 0x40 DUP4 ADD MLOAD PUSH2 0x793 PUSH1 0x40 DUP7 ADD DUP3 PUSH2 0x726 JUMP JUMPDEST POP PUSH1 0x60 DUP4 ADD MLOAD PUSH2 0x7A6 PUSH1 0x60 DUP7 ADD DUP3 PUSH2 0x73F JUMP JUMPDEST POP PUSH1 0x80 DUP4 ADD MLOAD PUSH2 0x7B9 PUSH1 0x80 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP DUP1 SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7D0 DUP4 DUP4 PUSH2 0x74E JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP SWAP2 SWAP1 POP JUMP JUMPDEST PUSH1 0x0 PUSH2 0x7F0 DUP3 PUSH2 0x643 JUMP JUMPDEST PUSH2 0x7FA DUP2 DUP6 PUSH2 0x64E JUMP JUMPDEST SWAP4 POP DUP4 PUSH1 0x20 DUP3 MUL DUP6 ADD PUSH2 0x80C DUP6 PUSH2 0x659 JUMP JUMPDEST DUP1 PUSH1 0x0 JUMPDEST DUP6 DUP2 LT ISZERO PUSH2 0x848 JUMPI DUP5 DUP5 SUB DUP10 MSTORE DUP2 MLOAD PUSH2 0x829 DUP6 DUP3 PUSH2 0x7C4 JUMP JUMPDEST SWAP5 POP PUSH2 0x834 DUP4 PUSH2 0x7D8 JUMP JUMPDEST SWAP3 POP PUSH1 0x20 DUP11 ADD SWAP10 POP POP PUSH1 0x1 DUP2 ADD SWAP1 POP PUSH2 0x810 JUMP JUMPDEST POP DUP3 SWAP8 POP DUP8 SWAP6 POP POP POP POP POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x874 DUP2 DUP5 PUSH2 0x7E5 JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH2 0x88A DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP2 EQ PUSH2 0x895 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 CALLDATALOAD SWAP1 POP PUSH2 0x8A7 DUP2 PUSH2 0x881 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0x8C3 JUMPI PUSH2 0x8C2 PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0x8D1 DUP5 DUP3 DUP6 ADD PUSH2 0x898 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0xA0 DUP4 ADD PUSH1 0x0 DUP4 ADD MLOAD DUP5 DUP3 SUB PUSH1 0x0 DUP7 ADD MSTORE PUSH2 0x8F7 DUP3 DUP3 PUSH2 0x6BA JUMP JUMPDEST SWAP2 POP POP PUSH1 0x20 DUP4 ADD MLOAD PUSH2 0x90C PUSH1 0x20 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP PUSH1 0x40 DUP4 ADD MLOAD PUSH2 0x91F PUSH1 0x40 DUP7 ADD DUP3 PUSH2 0x726 JUMP JUMPDEST POP PUSH1 0x60 DUP4 ADD MLOAD PUSH2 0x932 PUSH1 0x60 DUP7 ADD DUP3 PUSH2 0x73F JUMP JUMPDEST POP PUSH1 0x80 DUP4 ADD MLOAD PUSH2 0x945 PUSH1 0x80 DUP7 ADD DUP3 PUSH2 0x703 JUMP JUMPDEST POP DUP1 SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP DUP2 DUP2 SUB PUSH1 0x0 DUP4 ADD MSTORE PUSH2 0x96A DUP2 DUP5 PUSH2 0x8DA JUMP JUMPDEST SWAP1 POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x32 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH2 0x9AA DUP2 PUSH2 0x6F3 JUMP JUMPDEST DUP3 MSTORE POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 ADD SWAP1 POP PUSH2 0x9C5 PUSH1 0x0 DUP4 ADD DUP5 PUSH2 0x9A1 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH2 0x9D4 DUP2 PUSH2 0x712 JUMP JUMPDEST DUP2 EQ PUSH2 0x9DF JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0x9F1 DUP2 PUSH2 0x9CB JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xA0D JUMPI PUSH2 0xA0C PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0xA1B DUP5 DUP3 DUP6 ADD PUSH2 0x9E2 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 DUP2 MLOAD SWAP1 POP PUSH2 0xA33 DUP2 PUSH2 0x881 JUMP JUMPDEST SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH1 0x0 PUSH1 0x20 DUP3 DUP5 SUB SLT ISZERO PUSH2 0xA4F JUMPI PUSH2 0xA4E PUSH2 0x87C JUMP JUMPDEST JUMPDEST PUSH1 0x0 PUSH2 0xA5D DUP5 DUP3 DUP6 ADD PUSH2 0xA24 JUMP JUMPDEST SWAP2 POP POP SWAP3 SWAP2 POP POP JUMP JUMPDEST PUSH32 0x4E487B7100000000000000000000000000000000000000000000000000000000 PUSH1 0x0 MSTORE PUSH1 0x12 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x0 PUSH2 0xAA0 DUP3 PUSH2 0x735 JUMP JUMPDEST SWAP2 POP PUSH2 0xAAB DUP4 PUSH2 0x735 JUMP JUMPDEST SWAP3 POP DUP3 PUSH2 0xABB JUMPI PUSH2 0xABA PUSH2 0xA66 JUMP JUMPDEST JUMPDEST DUP3 DUP3 DIV SWAP1 POP SWAP3 SWAP2 POP POP JUMP INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 MSTORE8 STATICCALL PUSH9 0xF506445419B3AFB491 PUSH29 0x441141FBF14A060B975503BB970B737476AA3264736F6C634300081C00 CALLER ", "sourceMap": "209:1761:0:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;920:603;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;544:366;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;920:603;967:15;;:::i;:::-;994:22;;:::i;:::-;1026:23;:49;;;;;;;;1060:1;1026:49;;;;;;;;1064:1;1026:49;;;;;;1067:1;1026:49;;;;;;1070:1;1026:49;;;;;;1073:1;1026:49;;;;;;;1128:6;1123:371;1144:1;1140;:5;1123:371;;;1166:15;269:42;1184:15;;;1200:6;1207:1;1200:9;;;;;;;:::i;:::-;;;;;;1184:26;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1166:44;;1236:247;;;;;;;;1268:20;1278:6;1285:1;1278:9;;;;;;;:::i;:::-;;;;;;1268;:20::i;:::-;1236:247;;;;1315:6;1322:1;1315:9;;;;;;;:::i;:::-;;;;;;1236:247;;;;;;1352:8;1236:247;;;;;;1394:21;1406:8;1394:11;:21::i;:::-;1236:247;;;;269:42;1446:20;;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;1236:247;;;;;1224:6;1231:1;1224:9;;;;;;;:::i;:::-;;;;;:259;;;;1152:342;1147:3;;;;;;;1123:371;;;;1510:6;1503:13;;;;920:603;:::o;544:366::-;601:12;;:::i;:::-;625:15;269:42;643:15;;;659:7;643:24;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;625:42;;684:219;;;;;;;;712:18;722:7;712:9;:18::i;:::-;684:219;;;;753:7;684:219;;;;;;784:8;684:219;;;;;;822:21;834:8;822:11;:21::i;:::-;684:219;;;;269:42;870:20;;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;684:219;;;;;677:226;;;544:366;;;:::o;1660:308::-;1718:13;1758:1;1747:7;:12;;;1743:30;;1761:12;;;;;;;;;;;;;;;;;;;;;1743:30;1798:1;1787:7;:12;;;1783:30;;1801:12;;;;;;;;;;;;;;;;;;;;;1783:30;1838:1;1827:7;:12;;;1823:30;;1841:12;;;;;;;;;;;;;;;;;;;;;1823:30;1878:1;1867:7;:12;;;1863:31;;1881:13;;;;;;;;;;;;;;;;;;;;;1863:31;1919:1;1908:7;:12;;;1904:31;;1922:13;;;;;;;;;;;;;;;;;;;;;1904:31;1945:16;;;;;;;;;;;;;;;;;;;1660:308;;;;:::o;1533:117::-;1594:7;1640:3;1628:8;1620:17;;:23;;;;:::i;:::-;1613:30;;1533:117;;;:::o;-1:-1:-1:-;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;7:125:1:-;93:6;121:4;111:14;;7:125;;;:::o;138:164::-;256:11;293:3;278:18;;138:164;;;;:::o;308:119::-;394:4;417:3;409:11;;308:119;;;:::o;433:99::-;485:6;519:5;513:12;503:22;;433:99;;;:::o;538:159::-;612:11;646:6;641:3;634:19;686:4;681:3;677:14;662:29;;538:159;;;;:::o;703:248::-;785:1;795:113;809:6;806:1;803:13;795:113;;;894:1;889:3;885:11;879:18;875:1;870:3;866:11;859:39;831:2;828:1;824:10;819:15;;795:113;;;942:1;933:6;928:3;924:16;917:27;765:186;703:248;;;:::o;957:102::-;998:6;1049:2;1045:7;1040:2;1033:5;1029:14;1025:28;1015:38;;957:102;;;:::o;1065:357::-;1143:3;1171:39;1204:5;1171:39;:::i;:::-;1226:61;1280:6;1275:3;1226:61;:::i;:::-;1219:68;;1296:65;1354:6;1349:3;1342:4;1335:5;1331:16;1296:65;:::i;:::-;1386:29;1408:6;1386:29;:::i;:::-;1381:3;1377:39;1370:46;;1147:275;1065:357;;;;:::o;1428:93::-;1464:7;1504:10;1497:5;1493:22;1482:33;;1428:93;;;:::o;1527:105::-;1602:23;1619:5;1602:23;:::i;:::-;1597:3;1590:36;1527:105;;:::o;1638:101::-;1674:7;1714:18;1707:5;1703:30;1692:41;;1638:101;;;:::o;1745:105::-;1820:23;1837:5;1820:23;:::i;:::-;1815:3;1808:36;1745:105;;:::o;1856:77::-;1893:7;1922:5;1911:16;;1856:77;;;:::o;1939:108::-;2016:24;2034:5;2016:24;:::i;:::-;2011:3;2004:37;1939:108;;:::o;2111:1130::-;2212:3;2248:4;2243:3;2239:14;2337:4;2330:5;2326:16;2320:23;2390:3;2384:4;2380:14;2373:4;2368:3;2364:14;2357:38;2416:73;2484:4;2470:12;2416:73;:::i;:::-;2408:81;;2263:237;2585:4;2578:5;2574:16;2568:23;2604:61;2659:4;2654:3;2650:14;2636:12;2604:61;:::i;:::-;2510:165;2761:4;2754:5;2750:16;2744:23;2780:61;2835:4;2830:3;2826:14;2812:12;2780:61;:::i;:::-;2685:166;2943:4;2936:5;2932:16;2926:23;2962:63;3019:4;3014:3;3010:14;2996:12;2962:63;:::i;:::-;2861:174;3124:4;3117:5;3113:16;3107:23;3143:61;3198:4;3193:3;3189:14;3175:12;3143:61;:::i;:::-;3045:169;3231:4;3224:11;;2217:1024;2111:1130;;;;:::o;3247:240::-;3358:10;3393:88;3477:3;3469:6;3393:88;:::i;:::-;3379:102;;3247:240;;;;:::o;3493:132::-;3582:4;3614;3609:3;3605:14;3597:22;;3493:132;;;:::o;3695:1067::-;3852:3;3881:73;3948:5;3881:73;:::i;:::-;3970:105;4068:6;4063:3;3970:105;:::i;:::-;3963:112;;4101:3;4146:4;4138:6;4134:17;4129:3;4125:27;4176:75;4245:5;4176:75;:::i;:::-;4274:7;4305:1;4290:427;4315:6;4312:1;4309:13;4290:427;;;4386:9;4380:4;4376:20;4371:3;4364:33;4437:6;4431:13;4465:106;4566:4;4551:13;4465:106;:::i;:::-;4457:114;;4594:79;4666:6;4594:79;:::i;:::-;4584:89;;4702:4;4697:3;4693:14;4686:21;;4350:367;4337:1;4334;4330:9;4325:14;;4290:427;;;4294:14;4733:4;4726:11;;4753:3;4746:10;;3857:905;;;;;3695:1067;;;;:::o;4768:449::-;4949:4;4987:2;4976:9;4972:18;4964:26;;5036:9;5030:4;5026:20;5022:1;5011:9;5007:17;5000:47;5064:146;5205:4;5196:6;5064:146;:::i;:::-;5056:154;;4768:449;;;;:::o;5304:117::-;5413:1;5410;5403:12;5550:120;5622:23;5639:5;5622:23;:::i;:::-;5615:5;5612:34;5602:62;;5660:1;5657;5650:12;5602:62;5550:120;:::o;5676:137::-;5721:5;5759:6;5746:20;5737:29;;5775:32;5801:5;5775:32;:::i;:::-;5676:137;;;;:::o;5819:327::-;5877:6;5926:2;5914:9;5905:7;5901:23;5897:32;5894:119;;;5932:79;;:::i;:::-;5894:119;6052:1;6077:52;6121:7;6112:6;6101:9;6097:22;6077:52;:::i;:::-;6067:62;;6023:116;5819:327;;;;:::o;6210:1140::-;6321:3;6357:4;6352:3;6348:14;6446:4;6439:5;6435:16;6429:23;6499:3;6493:4;6489:14;6482:4;6477:3;6473:14;6466:38;6525:73;6593:4;6579:12;6525:73;:::i;:::-;6517:81;;6372:237;6694:4;6687:5;6683:16;6677:23;6713:61;6768:4;6763:3;6759:14;6745:12;6713:61;:::i;:::-;6619:165;6870:4;6863:5;6859:16;6853:23;6889:61;6944:4;6939:3;6935:14;6921:12;6889:61;:::i;:::-;6794:166;7052:4;7045:5;7041:16;7035:23;7071:63;7128:4;7123:3;7119:14;7105:12;7071:63;:::i;:::-;6970:174;7233:4;7226:5;7222:16;7216:23;7252:61;7307:4;7302:3;7298:14;7284:12;7252:61;:::i;:::-;7154:169;7340:4;7333:11;;6326:1024;6210:1140;;;;:::o;7356:357::-;7491:4;7529:2;7518:9;7514:18;7506:26;;7578:9;7572:4;7568:20;7564:1;7553:9;7549:17;7542:47;7606:100;7701:4;7692:6;7606:100;:::i;:::-;7598:108;;7356:357;;;;:::o;7719:180::-;7767:77;7764:1;7757:88;7864:4;7861:1;7854:15;7888:4;7885:1;7878:15;7905:115;7990:23;8007:5;7990:23;:::i;:::-;7985:3;7978:36;7905:115;;:::o;8026:218::-;8117:4;8155:2;8144:9;8140:18;8132:26;;8168:69;8234:1;8223:9;8219:17;8210:6;8168:69;:::i;:::-;8026:218;;;;:::o;8250:120::-;8322:23;8339:5;8322:23;:::i;:::-;8315:5;8312:34;8302:62;;8360:1;8357;8350:12;8302:62;8250:120;:::o;8376:141::-;8432:5;8463:6;8457:13;8448:22;;8479:32;8505:5;8479:32;:::i;:::-;8376:141;;;;:::o;8523:349::-;8592:6;8641:2;8629:9;8620:7;8616:23;8612:32;8609:119;;;8647:79;;:::i;:::-;8609:119;8767:1;8792:63;8847:7;8838:6;8827:9;8823:22;8792:63;:::i;:::-;8782:73;;8738:127;8523:349;;;;:::o;8878:141::-;8934:5;8965:6;8959:13;8950:22;;8981:32;9007:5;8981:32;:::i;:::-;8878:141;;;;:::o;9025:349::-;9094:6;9143:2;9131:9;9122:7;9118:23;9114:32;9111:119;;;9149:79;;:::i;:::-;9111:119;9269:1;9294:63;9349:7;9340:6;9329:9;9325:22;9294:63;:::i;:::-;9284:73;;9240:127;9025:349;;;;:::o;9380:180::-;9428:77;9425:1;9418:88;9525:4;9522:1;9515:15;9549:4;9546:1;9539:15;9752:185;9792:1;9809:20;9827:1;9809:20;:::i;:::-;9804:25;;9843:20;9861:1;9843:20;:::i;:::-;9838:25;;9882:1;9872:35;;9887:18;;:::i;:::-;9872:35;9929:1;9926;9922:9;9917:14;;9752:185;;;;:::o"}, "methodIdentifiers": {"getAllPrices()": "445df9d6", "getPrice(uint32)": "da26663a"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"getAllPrices\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"assetId\",\"type\":\"uint32\"},{\"internalType\":\"uint64\",\"name\":\"rawPrice\",\"type\":\"uint64\"},{\"internalType\":\"uint256\",\"name\":\"formattedPrice\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"}],\"internalType\":\"struct ReadPrices.Price[5]\",\"name\":\"\",\"type\":\"tuple[5]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"assetId\",\"type\":\"uint32\"}],\"name\":\"getPrice\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint32\",\"name\":\"assetId\",\"type\":\"uint32\"},{\"internalType\":\"uint64\",\"name\":\"rawPrice\",\"type\":\"uint64\"},{\"internalType\":\"uint256\",\"name\":\"formattedPrice\",\"type\":\"uint256\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"}],\"internalType\":\"struct ReadPrices.Price\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/ReadPrices.sol\":\"ReadPrices\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/ReadPrices.sol\":{\"keccak256\":\"0xe8e6e02c0c07822ef8d599de78042ec69fe3255f291b442ed99a1ddf029dbc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8a47e816c5e5ad4a7a7fa51025c14d5e26db23bf299f3146daac9522c56ee2d9\",\"dweb:/ipfs/QmfBdHkFsxnBiqn3mx76KLPe42uSdBY8K6kUZrqZXPsxRr\"]}},\"version\":1}"}}}}}