// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

contract Please {
    
    // Find which asset indices work on testnet
    function findWorkingAssets() external view returns (uint32[] memory workingIndices, uint[] memory rawPrices) {
        uint32[] memory tempIndices = new uint32[](20);
        uint[] memory tempPrices = new uint[](20);
        uint count = 0;
        
        // Test indices 0-19
        for (uint32 i = 0; i < 30; i++) {
            (bool success, bytes memory result) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(i));
            
            if (success) {
                uint64 price = abi.decode(result, (uint64));
                tempIndices[count] = i;
                tempPrices[count] = uint(price);
                count++;
            }
        }
        
        // Return only working assets
        workingIndices = new uint32[](count);
        rawPrices = new uint[](count);
        for (uint i = 0; i < count; i++) {
            workingIndices[i] = tempIndices[i];
            rawPrices[i] = tempPrices[i];
        }
    }
    
    // Your simple working function
    function getPrice(uint32 assetIndex) external view returns (uint) {
        (bool success, bytes memory result) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(assetIndex));
        require(success, "Asset doesn't exist");
        
        uint64 rawPrice = abi.decode(result, (uint64));
        return uint(rawPrice) / 10; // Your working conversion
    }
}