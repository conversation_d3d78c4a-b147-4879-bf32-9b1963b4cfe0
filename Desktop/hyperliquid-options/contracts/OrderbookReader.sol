// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract OrderBookReader {
    address constant ORACLE_PX_PRECOMPILE = 0x0000000000000000000000000000000000000807;
    
    function getOraclePrice(uint32 assetIndex) public view returns (uint256) {
        (bool success, bytes memory result) = ORACLE_PX_PRECOMPILE.staticcall(
            abi.encode(assetIndex)
        );
        require(success, "Oracle price query failed");
        return abi.decode(result, (uint256));
    }
}