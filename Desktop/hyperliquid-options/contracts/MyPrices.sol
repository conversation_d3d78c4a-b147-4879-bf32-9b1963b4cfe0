// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

contract MyPrices {
    mapping(uint32 => uint) public prices;
    mapping(uint32 => string) public assets;

    event PriceUpdated(uint32 indexed perpIndex, uint price);

    function getPrice(uint32 perpIndex) public returns (uint) {
        // Get oracle price directly from precompile
        (bool success1, bytes memory result1) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(perpIndex));
        require(success1, "Oracle price call failed");
        uint64 rawPrices = abi.decode(result1, (uint64));

        // Get asset info directly from precompile  
        (bool success2, bytes memory result2) = address(0x000000000000000000000000000000000000080a).staticcall(abi.encode(perpIndex));
        require(success2, "Asset info call failed");
        (string memory coin,, uint8 szDecimals,,) = abi.decode(result2, (string, uint32, uint8, uint8, bool));

        assets[perpIndex] = coin;

        uint divisor = 10 ** (6 - szDecimals);
        uint convertedPrice = (uint(rawPrices) * 1e18) / divisor;

        prices[perpIndex] = convertedPrice;
        emit PriceUpdated(perpIndex, convertedPrice);

        return convertedPrice;
    }

    function getLatestPrices(uint32 perpIndex) external view returns (uint) {
        return prices[perpIndex];
    }

    function getPriceNow(uint32 assetIndex) external view returns (uint) {
        (bool success, bytes memory result) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(assetIndex));
        require(success, "Failed to get price");
        
        uint64 rawPrice = abi.decode(result, (uint64));
        uint price = uint(rawPrice) / 10;
        
        return price;
    }
}