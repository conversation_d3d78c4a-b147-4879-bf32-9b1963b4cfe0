// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

interface IL1Read {
    function oraclePx(uint32 index) external view returns (uint64);
    function l1BlockNumber() external view returns (uint32);
}

contract PriceReader {
    IL1Read constant l1Read = IL1Read(0x0000000000000000000000000000000000000800); // L1 is a precompile deployed on the Hyperliquid Testnet
    
    struct Price {
        string symbol;
        uint32 assetId;
        uint64 rawPrice;
        uint256 formattedPrice; 
        uint32 blockNumber;
    }
    
    function getPrice(uint32 assetId) external view returns (Price memory) {
        uint64 rawPrice = l1Read.oraclePx(assetId);
        return Price({
            symbol: getSymbol(assetId),
            assetId: assetId,
            rawPrice: rawPrice,
            formattedPrice: formatPrice(rawPrice),
            blockNumber: l1Read.l1BlockNumber()
        });
    }
    
    function getAllPrices() external view returns (Price[5] memory) {
        Price[5] memory prices;
        uint32[5] memory assets = [uint32(0), 1, 2, 3, 4]; // BTC, ETH, SOL, AVAX, HYPE
        
        for (uint i = 0; i < 5; i++) {
            uint64 rawPrice = l1Read.oraclePx(assets[i]);
            prices[i] = Price({
                symbol: getSymbol(assets[i]),
                assetId: assets[i],
                rawPrice: rawPrice,
                formattedPrice: formatPrice(rawPrice),
                blockNumber: l1Read.l1BlockNumber()
            });
        }
        return prices;
    }
    
    function formatPrice(uint64 rawPrice) internal pure returns (uint256) {
        return uint256(rawPrice) / 100;
    }
    
    function getSymbol(uint32 assetId) internal pure returns (string memory) {
        if (assetId == 0) return "BTC";
        if (assetId == 1) return "ETH";
        if (assetId == 2) return "SOL";
        if (assetId == 3) return "AVAX";
        if (assetId == 4) return "HYPE";
        return "UNKNOWN";
    }
}