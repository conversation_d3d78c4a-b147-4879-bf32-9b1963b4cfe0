// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

import ./L1Read.sol;

interface L1Read {
    function oraclePx(uint32 index ) external view returns (uint);
}


contract ReadPrices is L1Read {
    mapping(uint32 => uint) public prices;
    mapping(uint32 => string) public assets;

    event PriceUpdated(uint32 indexed perpIndex, uint price);

    function getPrice(uint32 perpIndex) public view returns (uint) {
        uint rawPrices = oraclePx(perpIndex); // get oracle price 

        AssetInfo memory assetInfo = getPerpAssetInfo(perpIndex);
        uint decimals = assetInfo.szDecimals;

        assetNames[perpIndex] = assetInfo.coin;

        uint divisor = 10 ** (6 - szDecimals);
        uint convertedPrice = 10 ** (8 - rawPrices / divisor);

        prices[perpIndex] = convertedPrice;

        emit PriceUpdated(perpIndex, convertedPrice);

        return convertedPrice;
    }

    function getLatestPrices(uint32 perpIndex) external view returns (uint) {
        return prices[perpIndex];
    }

}