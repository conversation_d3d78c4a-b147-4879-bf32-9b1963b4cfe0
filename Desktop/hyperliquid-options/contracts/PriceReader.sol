// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

contract PriceReader {
    
    function getPriceRaw(uint32 assetIndex) external view returns (uint) {
        //oracle precompile address 0x0000000000000000000000000000000000000807
        (bool success1, bytes memory result1) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(assetIndex));
        require(success1, "Failed to get price");
        uint64 rawPrice = abi.decode(result1, (uint64));
        
        (bool success2, bytes memory result2) = address(0x000000000000000000000000000000000000080a).staticcall(abi.encode(assetIndex));
        require(success2, "Failed to get asset info");
        (,, uint8 szDecimals,,) = abi.decode(result2, (string, uint32, uint8, uint8, bool));
        
        uint divisor = 10 ** (6 - szDecimals);
        uint price = uint(rawPrice) / divisor;
        
        return price;  // expect Returns like 96036 for $96,036
    }
    
    function getPrice18Decimals(uint32 assetIndex) external view returns (uint) {
        (bool success1, bytes memory result1) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(assetIndex));
        require(success1, "Failed to get price");
        uint64 rawPrice = abi.decode(result1, (uint64));
        
        (bool success2, bytes memory result2) = address(0x000000000000000000000000000000000000080a).staticcall(abi.encode(assetIndex));
        require(success2, "Failed to get asset info");
        (,, uint8 szDecimals,,) = abi.decode(result2, (string, uint32, uint8, uint8, bool));
        
        // EXAMPLE VERSION: (rawPrice * 10^18) / 10^(6 - szDecimals)
        uint divisor = 10 ** (6 - szDecimals);
        uint convertedPrice = (uint(rawPrice) * 1e18) / divisor;
        
        return convertedPrice; // Returns with 18 decimals
    }

   
}