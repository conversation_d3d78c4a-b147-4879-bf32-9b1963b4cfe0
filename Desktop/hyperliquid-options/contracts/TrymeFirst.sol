// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

contract TrymeFirst {
    
    // Get all asset prices and try to get their names
    function discoverAssets() external view returns (
        uint32[] memory indices,
        uint[] memory prices,
        string[] memory names,
        bool[] memory hasNames
    ) {
        indices = new uint32[](20);
        prices = new uint[](20);
        names = new string[](20);
        hasNames = new bool[](20);
        
        for (uint32 i = 0; i < 20; i++) {
            indices[i] = i;
            
            // Get price
            (bool priceSuccess, bytes memory priceResult) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(i));
            if (priceSuccess) {
                uint64 rawPrice = abi.decode(priceResult, (uint64));
                prices[i] = uint(rawPrice) / 10;
            }
            
            // Try to get name from perpAssetInfo
            (bool nameSuccess, bytes memory nameResult) = address(0x000000000000000000000000000000000000080a).staticcall(abi.encode(i));
            if (nameSuccess) {
                try this.safeDecode(nameResult) returns (string memory coin) {
                    names[i] = coin;
                    hasNames[i] = true;
                } catch {
                    names[i] = "Unknown";
                    hasNames[i] = false;
                }
            } else {
                names[i] = "Unknown";
                hasNames[i] = false;
            }
        }
    }
    
    // Safe decode helper
    function safeDecode(bytes memory data) external pure returns (string memory) {
        (string memory coin,,,,) = abi.decode(data, (string, uint32, uint8, uint8, bool));
        return coin;
    }
    
    // Find specific asset by checking price ranges
    function findAssetByPrice(uint targetPrice, uint tolerance) external view returns (uint32[] memory possibleIndices) {
        uint32[] memory temp = new uint32[](20);
        uint count = 0;
        
        for (uint32 i = 0; i < 20; i++) {
            (bool success, bytes memory result) = address(0x0000000000000000000000000000000000000807).staticcall(abi.encode(i));
            if (success) {
                uint64 rawPrice = abi.decode(result, (uint64));
                uint price = uint(rawPrice) / 10;
                
                if (price >= targetPrice - tolerance && price <= targetPrice + tolerance) {
                    temp[count] = i;
                    count++;
                }
            }
        }
        
        possibleIndices = new uint32[](count);
        for (uint i = 0; i < count; i++) {
            possibleIndices[i] = temp[i];
        }
    }
}