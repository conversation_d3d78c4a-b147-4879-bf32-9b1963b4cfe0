import { viem } from "hardhat";

// dont need to deploy L1 as it already exists on the network
async function main() {
  console.log("Deploying contracts.... 🦊");
  const [deployer] = await viem.getWalletClients();
  console.log("Deployer address:", deployer.account.address);

  const ReadPrices = await viem.deployContract("ReadPrices");
  console.log("ReadPrices deployed to:", ReadPrices.address);

  console.log("\nContract Addresses:");
  console.log("ReadPrices:", ReadPrices.address);
  console.log("Deployer:", deployer.account.address);
}

main().catch((error) => {
  console.error(error);
  process.exit(1);
});
